#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复P.pull.py中objective函数的缩进问题
"""

import re

def fix_objective_function_indentation():
    """修复objective函数的缩进"""
    
    # 读取文件
    with open('P.pull.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到objective函数的开始和结束
    objective_start = None
    objective_end = None
    
    for i, line in enumerate(lines):
        if 'def objective(trial):' in line:
            objective_start = i
            print(f"找到objective函数开始: 第{i+1}行")
        elif objective_start is not None and line.strip().startswith('def ') and 'objective' not in line:
            objective_end = i
            print(f"找到objective函数结束: 第{i+1}行")
            break
    
    if objective_start is None:
        print("未找到objective函数")
        return False
    
    if objective_end is None:
        # 如果没找到下一个函数，查找到文件末尾
        for i in range(objective_start + 1, len(lines)):
            if lines[i].strip() and not lines[i].startswith(' ') and not lines[i].startswith('\t'):
                objective_end = i
                break
        if objective_end is None:
            objective_end = len(lines)
    
    print(f"objective函数范围: 第{objective_start+1}行 到 第{objective_end}行")
    
    # 修复缩进
    fixed_lines = []
    in_try_block = False
    base_indent = '    '  # 函数基础缩进
    try_indent = '        '  # try块缩进
    
    for i in range(len(lines)):
        if i < objective_start or i >= objective_end:
            # 不在objective函数范围内，保持原样
            fixed_lines.append(lines[i])
        else:
            line = lines[i]
            stripped = line.strip()
            
            if i == objective_start:
                # 函数定义行
                fixed_lines.append(line)
            elif stripped.startswith('"""') and '优化目标函数' in stripped:
                # 文档字符串
                fixed_lines.append(base_indent + stripped + '\n')
            elif stripped.startswith('nonlocal'):
                fixed_lines.append(base_indent + stripped + '\n')
            elif stripped.startswith('# ✅ 修复：添加错误处理和文件清理'):
                fixed_lines.append(base_indent + stripped + '\n')
            elif stripped.startswith('import uuid') or stripped.startswith('import tempfile'):
                fixed_lines.append(base_indent + stripped + '\n')
            elif stripped.startswith('trial_id =') or stripped.startswith('temp_files ='):
                fixed_lines.append(base_indent + stripped + '\n')
            elif stripped == '':
                # 空行保持
                fixed_lines.append(line)
            elif stripped.startswith('try:'):
                fixed_lines.append(base_indent + stripped + '\n')
                in_try_block = True
            elif stripped.startswith('except') or stripped.startswith('finally'):
                fixed_lines.append(base_indent + stripped + '\n')
                in_try_block = False
            elif in_try_block:
                # 在try块内，使用try块缩进
                if stripped.startswith('#'):
                    # 注释
                    fixed_lines.append(try_indent + stripped + '\n')
                elif stripped.startswith('def '):
                    # 内部函数定义
                    fixed_lines.append(try_indent + stripped + '\n')
                elif stripped.startswith('"""'):
                    # 内部函数文档字符串
                    fixed_lines.append(try_indent + '    ' + stripped + '\n')
                elif stripped.startswith('if ') or stripped.startswith('elif ') or stripped.startswith('else:'):
                    # 条件语句
                    fixed_lines.append(try_indent + stripped + '\n')
                elif stripped.startswith('for ') or stripped.startswith('while '):
                    # 循环语句
                    fixed_lines.append(try_indent + stripped + '\n')
                elif stripped.startswith('return '):
                    # return语句
                    fixed_lines.append(try_indent + stripped + '\n')
                else:
                    # 其他语句，根据上下文确定缩进
                    # 简单规则：如果前一行是控制结构，增加缩进
                    prev_line = fixed_lines[-1].strip() if fixed_lines else ''
                    if (prev_line.endswith(':') and 
                        (prev_line.startswith('if ') or prev_line.startswith('elif ') or 
                         prev_line.startswith('else:') or prev_line.startswith('for ') or 
                         prev_line.startswith('while ') or prev_line.startswith('def ') or
                         prev_line.startswith('try:') or prev_line.startswith('except') or
                         prev_line.startswith('finally:'))):
                        fixed_lines.append(try_indent + '    ' + stripped + '\n')
                    else:
                        fixed_lines.append(try_indent + stripped + '\n')
            else:
                # 不在try块内，使用基础缩进
                fixed_lines.append(base_indent + stripped + '\n')
    
    # 写回文件
    with open('P.pull.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print("✅ objective函数缩进修复完成")
    return True

def main():
    """主函数"""
    print("🔧 开始修复objective函数缩进...")
    
    if fix_objective_function_indentation():
        print("🎉 缩进修复完成！")
        return True
    else:
        print("❌ 缩进修复失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
