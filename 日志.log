 2025-01-01
2025-08-04 22:12:49,834 - INFO - 🔒 步骤2: 严格按时间进行数据分割（防止数据泄漏）
2025-08-04 22:12:50,888 - INFO - 🔒 时间分割结果:
2025-08-04 22:12:50,888 - INFO -   训练集: 94372行 (2025-06-04之前)
2025-08-04 22:12:50,889 - INFO -   验证集: 113750行 (2025-06-04~2025-07-03)
2025-08-04 22:12:50,889 - INFO -   测试集: 120605行 (2025-07-03之后)
2025-08-04 22:12:50,889 - INFO - 🔒 步骤3: 只基于训练集计算安全特征
2025-08-04 22:12:51,041 - INFO - 🔒 步骤4: 为首板策略安全生成目标变量
2025-08-04 22:12:51,517 - WARNING - ⚠️ 验证集/测试集中有5409个future_1_day_pct_chg为NaN，这是正常的边界情况
2025-08-04 22:12:51,528 - INFO -   ⚠️ 注意：验证集/测试集的目标变量仅用于模型评估，不用于特征工程
2025-08-04 22:12:51,854 - WARNING - ⚠️ 验证集/测试集中有5418个future_1_day_pct_chg为NaN，这是正常的边界情况
2025-08-04 22:12:51,865 - INFO -   ⚠️ 注意：验证集/测试集的目标变量仅用于模型评估，不用于特征工程
2025-08-04 22:12:51,945 - INFO - 🔒 添加安全的首板策略特征...
2025-08-04 22:12:52,407 - INFO - 🔒 从训练集中安全选择首板样本，使用增强样本生成...
2025-08-04 22:13:17,908 - INFO -   增强样本生成：从3151个基础样本生成了2901个增强样本
2025-08-04 22:13:17,953 - INFO - 📊 首板样本详细统计:
2025-08-04 22:13:17,953 - INFO -   训练集总数据: 94372行
2025-08-04 22:13:17,953 - INFO -   训练集首板样本: 2901个 (3.07%)
2025-08-04 22:13:52,522 - INFO -   增强样本生成：从4556个基础样本生成了4048个增强样本
2025-08-04 22:14:39,966 - INFO -   增强样本生成：从5983个基础样本生成了8658个增强样本
2025-08-04 22:14:40,095 - INFO -   验证集总数据: 113750行
2025-08-04 22:14:40,095 - INFO -   验证集首板样本: 4048个 (3.56%)
2025-08-04 22:14:40,095 - INFO -   测试集总数据: 120605行
2025-08-04 22:14:40,095 - INFO -   测试集首板样本: 8658个 (7.18%)
2025-08-04 22:14:40,095 - INFO - ✅ 首板样本收集完成，总计15607个样本
2025-08-04 22:14:40,095 - INFO - 📊 首板策略正负样本分布统计:
2025-08-04 22:14:40,102 - INFO -   ✅ 正样本(首板成功): 1242个 (24.0%)
2025-08-04 22:14:40,103 - INFO -   ❌ 负样本(首板失败): 3942个 (76.0%)
2025-08-04 22:14:40,103 - INFO -   ⚪ 中性样本: 10423个
2025-08-04 22:14:40,103 - INFO -   📈 样本平衡比例: 1242:3942 (正:负)
2025-08-04 22:14:40,103 - INFO -   ⚠️ 样本轻度不平衡 (平衡度: 0.32)，建议使用类别权重
2025-08-04 22:14:40,103 - INFO - 🔒 步骤5: 准备最终的训练数据
2025-08-04 22:14:40,103 - INFO - ✅ 首板策略总样本数: 15607
2025-08-04 22:14:40,120 - INFO - 训练集目标变量分布:
future_1_day_limit_up
False    8452
True     7155
Name: count, dtype: int64
2025-08-04 22:14:40,120 - INFO - 🔒 步骤6: 准备序列数据（时间序列格式）
2025-08-04 22:14:40,135 - INFO - 🔧 发现234个潜在特征列
2025-08-04 22:14:40,181 - WARNING - ⚠️ 跳过特征buy_sm_amount_mf：有效数据不足(0/15607)
2025-08-04 22:14:40,182 - WARNING - ⚠️ 跳过特征sell_sm_amount_mf：有效数据不足(0/15607)
2025-08-04 22:14:40,182 - WARNING - ⚠️ 跳过特征buy_md_amount_mf：有效数据不足(0/15607)
2025-08-04 22:14:40,182 - WARNING - ⚠️ 跳过特征sell_md_amount_mf：有效数据不足(0/15607)
2025-08-04 22:14:40,182 - WARNING - ⚠️ 跳过特征buy_lg_amount_mf：有效数据不足(0/15607)
2025-08-04 22:14:40,183 - WARNING - ⚠️ 跳过特征sell_lg_amount_mf：有效数据不足(0/15607)
2025-08-04 22:14:40,183 - WARNING - ⚠️ 跳过特征buy_elg_amount_mf：有效数据不足(0/15607)
2025-08-04 22:14:40,183 - WARNING - ⚠️ 跳过特征sell_elg_amount_mf：有效数据不足(0/15607)
2025-08-04 22:14:40,184 - WARNING - ⚠️ 跳过特征net_mf_amount_mf：有效数据不足(0/15607)
2025-08-04 22:14:40,189 - INFO - ✅ 筛选出225个有效特征
2025-08-04 22:14:40,189 - INFO - ✅ 使用pe_ttm作为pe特征
2025-08-04 22:14:40,189 - INFO - ✅ 使用227个有效特征
2025-08-04 22:14:40,189 - INFO - 🔧 开始生成序列数据，输入数据: 15607行, 序列长度: 10
2025-08-04 22:14:40,189 - INFO - 🔧 有效特征数量: 227
2025-08-04 22:14:46,175 - INFO - 🔒 步骤7: 执行最终的数据分割和返回
2025-08-04 22:14:46,175 - INFO - 🔧 数组长度统计: X=3220, y1_cls=3220, y1_reg=3220, y2_cls=3220, y2_reg=3220, weights=3220, 最小长度=3220
2025-08-04 22:14:46,176 - INFO - ✅ 所有数组长度已统一为: 3220
2025-08-04 22:14:46,188 - INFO - ✅ 最终数据形状: X=(3220, 10, 227), 样本权重=3220
2025-08-04 22:14:46,188 - INFO - 🔧 开始正确的时间序列分割...
2025-08-04 22:14:46,190 - INFO - 🔧 时间序列分割点:
2025-08-04 22:14:46,190 - INFO -   训练集: 2025-01-02 到 2025-06-05
2025-08-04 22:14:46,190 - INFO -   验证集: 2025-06-06 到 2025-06-27
2025-08-04 22:14:46,190 - INFO -   测试集: 2025-06-30 到 2025-07-30
2025-08-04 22:14:52,870 - INFO - 🔒 三分法安全分割完成:
2025-08-04 22:14:52,870 - INFO -   训练集: (133, 10, 227)
2025-08-04 22:14:52,870 - INFO -   验证集: (284, 10, 227)
2025-08-04 22:14:52,870 - INFO -   测试集: (2803, 10, 227)
2025-08-04 22:14:52,870 - INFO - 
📊 训练集详细样本统计 (首板策略):
2025-08-04 22:14:52,870 - INFO -   总样本数: 133
2025-08-04 22:14:52,870 - INFO -   分类输出1:
2025-08-04 22:14:52,870 - INFO -     负样本(值=0): 99个 (74.4%)
2025-08-04 22:14:52,870 - INFO -     正样本(值=1): 34个 (25.6%)
2025-08-04 22:14:52,870 - INFO -     ⚠️ 样本轻度不平衡 (平衡度: 0.34)
2025-08-04 22:14:52,870 - INFO -   回归输出1:
2025-08-04 22:14:52,871 - INFO -     有效样本: 133个 (缺失: 0个)
2025-08-04 22:14:52,871 - INFO -     均值: 2.896%
2025-08-04 22:14:52,871 - INFO -     标准差: 6.688%
2025-08-04 22:14:52,871 - INFO -     范围: [-10.480%, 10.996%]
2025-08-04 22:14:52,871 - INFO -     中位数: 3.716%
2025-08-04 22:14:52,871 - INFO -   分类输出2:
2025-08-04 22:14:52,871 - INFO -     负样本(值=0): 99个 (74.4%)
2025-08-04 22:14:52,871 - INFO -     正样本(值=1): 34个 (25.6%)
2025-08-04 22:14:52,871 - INFO -     ⚠️ 样本轻度不平衡 (平衡度: 0.34)
2025-08-04 22:14:52,871 - INFO -   回归输出2:
2025-08-04 22:14:52,871 - INFO -     有效样本: 133个 (缺失: 0个)
2025-08-04 22:14:52,872 - INFO -     均值: 2.896%
2025-08-04 22:14:52,872 - INFO -     标准差: 6.688%
2025-08-04 22:14:52,872 - INFO -     范围: [-10.480%, 10.996%]
2025-08-04 22:14:52,872 - INFO -     中位数: 3.716%
2025-08-04 22:14:52,872 - INFO - 
📊 验证集详细样本统计 (首板策略):
2025-08-04 22:14:52,872 - INFO -   总样本数: 284
2025-08-04 22:14:52,872 - INFO -   分类输出1:
2025-08-04 22:14:52,872 - INFO -     负样本(值=0): 153个 (53.9%)
2025-08-04 22:14:52,872 - INFO -     正样本(值=1): 131个 (46.1%)
2025-08-04 22:14:52,872 - INFO -     ✅ 样本平衡性良好 (平衡度: 0.86)
2025-08-04 22:14:52,872 - INFO -   回归输出1:
2025-08-04 22:14:52,872 - INFO -     有效样本: 284个 (缺失: 0个)
2025-08-04 22:14:52,872 - INFO -     均值: 4.241%
2025-08-04 22:14:52,873 - INFO -     标准差: 6.778%
2025-08-04 22:14:52,873 - INFO -     范围: [-10.480%, 10.996%]
2025-08-04 22:14:52,873 - INFO -     中位数: 6.758%
2025-08-04 22:14:52,873 - INFO -   分类输出2:
2025-08-04 22:14:52,873 - INFO -     负样本(值=0): 153个 (53.9%)
2025-08-04 22:14:52,873 - INFO -     正样本(值=1): 131个 (46.1%)
2025-08-04 22:14:52,873 - INFO -     ✅ 样本平衡性良好 (平衡度: 0.86)
2025-08-04 22:14:52,873 - INFO -   回归输出2:
2025-08-04 22:14:52,873 - INFO -     有效样本: 284个 (缺失: 0个)
2025-08-04 22:14:52,873 - INFO -     均值: 4.241%
2025-08-04 22:14:52,873 - INFO -     标准差: 6.778%
2025-08-04 22:14:52,874 - INFO -     范围: [-10.480%, 10.996%]
2025-08-04 22:14:52,874 - INFO -     中位数: 6.758%
2025-08-04 22:14:52,874 - INFO - 
📊 测试集详细样本统计 (首板策略):
2025-08-04 22:14:52,874 - INFO -   总样本数: 2803
2025-08-04 22:14:52,874 - INFO -   分类输出1:
2025-08-04 22:14:52,874 - INFO -     负样本(值=0): 632个 (22.5%)
2025-08-04 22:14:52,874 - INFO -     正样本(值=1): 2171个 (77.5%)
2025-08-04 22:14:52,874 - INFO -     🔴 样本严重不平衡 (平衡度: 0.29)
2025-08-04 22:14:52,874 - INFO -   回归输出1:
2025-08-04 22:14:52,875 - INFO -     有效样本: 2803个 (缺失: 0个)
2025-08-04 22:14:52,875 - INFO -     均值: 8.591%
2025-08-04 22:14:52,875 - INFO -     标准差: 4.219%
2025-08-04 22:14:52,875 - INFO -     范围: [-10.480%, 10.996%]
2025-08-04 22:14:52,875 - INFO -     中位数: 10.002%
2025-08-04 22:14:52,875 - INFO -   分类输出2:
2025-08-04 22:14:52,875 - INFO -     负样本(值=0): 632个 (22.5%)
2025-08-04 22:14:52,875 - INFO -     正样本(值=1): 2171个 (77.5%)
2025-08-04 22:14:52,875 - INFO -     🔴 样本严重不平衡 (平衡度: 0.29)
2025-08-04 22:14:52,876 - INFO -   回归输出2:
2025-08-04 22:14:52,876 - INFO -     有效样本: 2803个 (缺失: 0个)
2025-08-04 22:14:52,876 - INFO -     均值: 8.591%
2025-08-04 22:14:52,876 - INFO -     标准差: 4.219%
2025-08-04 22:14:52,876 - INFO -     范围: [-10.480%, 10.996%]
2025-08-04 22:14:52,876 - INFO -     中位数: 10.002%
2025-08-04 22:14:52,876 - INFO - 🔒 检查样本分布质量...
2025-08-04 22:14:52,876 - INFO - 训练集 classification_output_1分布: {0: 99, 1: 34}
2025-08-04 22:14:52,877 - INFO - 训练集 classification_output_2分布: {0: 99, 1: 34}
2025-08-04 22:14:52,877 - INFO - 验证集 classification_output_1分布: {0: 153, 1: 131}
2025-08-04 22:14:52,877 - INFO - 验证集 classification_output_2分布: {0: 153, 1: 131}
2025-08-04 22:14:52,877 - INFO - 测试集 classification_output_1分布: {0: 632, 1: 2171}
2025-08-04 22:14:52,877 - INFO - 测试集 classification_output_2分布: {0: 632, 1: 2171}
2025-08-04 22:14:52,877 - INFO - ✅ 样本分布质量检查通过
2025-08-04 22:14:52,878 - INFO -   classification_output_1 - 训练集分布: [99 34], 验证集分布: [153 131], 测试集分布: [ 632 2171]
2025-08-04 22:14:52,878 - INFO -   classification_output_2 - 训练集分布: [99 34], 验证集分布: [153 131], 测试集分布: [ 632 2171]
2025-08-04 22:14:52,878 - INFO - ✅ 首板策略数据准备完成，耗时: 125.21秒
2025-08-04 22:14:52,975 - INFO - 特征维度: 227个特征，10个时间步
2025-08-04 22:14:52,976 - INFO - ✅ 已将字典格式标签转换为列表格式用于超参数优化
2025-08-04 22:14:52,976 - INFO - 开始首板策略超参数优化...
2025-08-04 22:14:52,978 - INFO - 元学习初始化完成 | 有效记录: 0条
[I 2025-08-04 22:14:52,979] A new study created in memory with name: no-name-54c423e6-38fd-46a2-83fd-aa0b35140d8e
2025-08-04 22:14:52,979 - WARNING - objective函数: y_train是列表格式，转换为字典格式
2025-08-04 22:14:52,979 - WARNING - objective函数: y_test是列表格式，转换为字典格式
2025-08-04 22:14:52,983 - INFO - 专家网络单元数 32 小于 首板 策略建议值 64，但保持超参数优化器的选择
2025-08-04 22:14:52,989 - INFO - 🔧 首板策略智能映射：输入227维 -> 映射到128维
2025-08-04 22:14:54,483 - INFO - 编译模型配置，策略类型: 首板, 学习率: 1.1415338890724785e-05
2025-08-04 22:14:54,484 - INFO - 使用AdEMAMix优化器 - 2024年最新技术，混合双EMA设计
2025-08-04 22:14:54,512 - INFO - 监控指标已设置为动态匹配val_classification_output_1_auc，ReduceLROnPlateau已禁用，学习率由LearningRateScheduler全权控制
2025-08-04 22:14:54,512 - WARNING - y_train不是标准的列表/元组格式，尝试替代方法提取标签
2025-08-04 22:14:54,513 - INFO - regression_output_1稳健标准化后：均值=-0.0884，中位数=0.0000，标准差=0.7206，最小值=-1.5295，最大值=0.7845
2025-08-04 22:14:54,513 - INFO - regression_output_1稳健标准化后：均值=-0.0884，中位数=0.0000，标准差=0.7206，最小值=-1.5295，最大值=0.7845
2025-08-04 22:14:54,514 - INFO - regression_output_2稳健标准化后：均值=-0.0884，中位数=0.0000，标准差=0.7206，最小值=-1.5295，最大值=0.7845
2025-08-04 22:14:54,514 - INFO - regression_output_2稳健标准化后：均值=-0.0884，中位数=0.0000，标准差=0.7206，最小值=-1.5295，最大值=0.7845
2025-08-04 22:14:54,515 - INFO - regression_output_1稳健标准化后：均值=-14.1101，中位数=0.0000，标准差=42.1910，最小值=-204.8193，最大值=9.9380
2025-08-04 22:14:54,516 - INFO - regression_output_2稳健标准化后：均值=-14.1101，中位数=0.0000，标准差=42.1910，最小值=-204.8193，最大值=9.9380
2025-08-04 22:14:54,721 - WARNING - 无法找到与 val_classification_output_1_auc 匹配的指标，使用 val_loss 代替
Epoch 1/30
1/2 [==============>...............] - ETA: 24s - loss: 7.1430 - classification_output_1_loss: 0.2124 - regression_output_1_loss: 1.8853 - classification_output_2_loss: 0.2139 - regression_output_2_loss: 1.3975 - classification_output_1_binary_accuracy: 0.5859 - classification_output_1_classification_output_1_auc: 0.6208 - regression_output_1_mse: 1.8379 - classification_output_2_binary_accuracy: 0.6172 - classification_output_2_classification_output_2_auc: 0.6084 - regression_o2/2 [==============================] - ETA: 0s - loss: 7.0516 - classification_output_1_loss: 0.2090 - regression_output_1_loss: 1.8594 - classification_output_2_loss: 0.2102 - regression_output_2_loss: 1.3821 - classification_output_1_binary_accuracy: 0.5865 - classification_output_1_classification_output_1_auc: 0.6168 - regression_output_1_mse: 1.8052 - classification_output_2_binary_accuracy: 0.6165 - classification_output_2_classification_output_2_auc: 0.6035 - regression_output_2_mse: 1.1122 [W 2025-08-04 22:15:25,061] Trial 0 failed with parameters: {'param_strategy': 'history', 'lstm_units_1': 192, 'batch_size': 64, 'learning_rate': 0.01220776478695415, 'dropout_rate': 0.30000000000000004, 'l2_reg': 0.0006796578090758161, 'patience': 5, 'attention_heads': 12, 'num_experts_1': 7, 'expert_units_1': 32, 'num_experts_2': 2, 'expert_units_2': 16, 'combined_weight': 0.3042422429595377, 'base_learning_rate': 3.752055855124284e-05, 'kt_num_experts': 4, 'kt_expert_units': 60} because of the following error: ValueError('Unable to synchronously create dataset (name already exists)').
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/optuna/study/_optimize.py", line 201, in _run_trial
    value_or_values = func(trial)
  File "/home/<USER>/P.pull.py", line 5540, in objective
    history = model.fit(
  File "/home/<USER>/.local/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/.local/lib/python3.10/site-packages/h5py/_hl/group.py", line 186, in create_dataset
    dsid = dataset.make_new_dset(group, shape, dtype, data, name, **kwds)
  File "/home/<USER>/.local/lib/python3.10/site-packages/h5py/_hl/dataset.py", line 175, in make_new_dset
    dset_id = h5d.create(parent.id, name, tid, sid, dcpl=dcpl, dapl=dapl)
  File "h5py/_objects.pyx", line 56, in h5py._objects.with_phil.wrapper
  File "h5py/_objects.pyx", line 57, in h5py._objects.with_phil.wrapper
  File "h5py/h5d.pyx", line 136, in h5py.h5d.create
ValueError: Unable to synchronously create dataset (name already exists)
[W 2025-08-04 22:15:25,062] Trial 0 failed with value None.
2025-08-04 22:15:25,483 - ERROR - 超参数优化失败: Unable to synchronously create dataset (name already exists)
2025-08-04 22:15:25,487 - INFO - 已更新首板策略元学习历史记录，当前记录数: 1
2025-08-04 22:15:25,487 - ERROR - 获取最佳模型失败: No trials are completed yet.
2025-08-04 22:15:25,488 - ERROR - 清理试验资源时出错: No trials are completed yet.
2025-08-04 22:15:25,488 - INFO - 优化完成，最佳验证损失: inf
2025-08-04 22:15:25,488 - INFO - 超参数优化耗时: 32.51秒
2025-08-04 22:15:25,488 - INFO - 最佳参数已保存到 models/首板_best_params.pkl
2025-08-04 22:15:25,488 - INFO - 首板策略最佳超参数（完整）:
2025-08-04 22:15:25,488 - INFO -   lstm_units_1: 128
2025-08-04 22:15:25,488 - INFO -   lstm_units_2: 64
2025-08-04 22:15:25,488 - INFO -   attention_heads: 8
2025-08-04 22:15:25,488 - INFO -   dropout_rate: 0.2
2025-08-04 22:15:25,488 - INFO -   l2_reg: 0.001
2025-08-04 22:15:25,488 - INFO -   learning_rate: 0.0005
2025-08-04 22:15:25,488 - INFO -   batch_size: 128
2025-08-04 22:15:25,488 - INFO -   patience: 10
2025-08-04 22:15:25,488 - INFO -   num_experts_1: 4
2025-08-04 22:15:25,488 - INFO -   expert_units_1: 64
2025-08-04 22:15:25,489 - INFO -   num_experts_2: 2
2025-08-04 22:15:25,489 - INFO -   expert_units_2: 32
2025-08-04 22:15:25,489 - INFO - 首板策略最佳超参数JSON格式: {
  "lstm_units_1": 128,
  "lstm_units_2": 64,
  "attention_heads": 8,
  "dropout_rate": 0.2,
  "l2_reg": 0.001,
  "learning_rate": 0.0005,
  "batch_size": 128,
  "patience": 10,
  "num_experts_1": 4,
  "expert_units_1": 64,
  "num_experts_2": 2,
  "expert_units_2": 32
}
2025-08-04 22:15:25,489 - INFO - 首板策略最佳超参数: {'lstm_units_1': 128, 'lstm_units_2': 64, 'attention_heads': 8, 'dropout_rate': 0.2, 'l2_reg': 0.001, 'learning_rate': 0.0005, 'batch_size': 128, 'patience': 10, 'num_experts_1': 4, 'expert_units_1': 64, 'num_experts_2': 2, 'expert_units_2': 32}
2025-08-04 22:15:25,489 - INFO - 开始首板策略模型部署...
2025-08-04 22:15:25,489 - INFO - 超参数优化过程未返回模型，构建新模型...
2025-08-04 22:15:25,489 - INFO - 🔧 使用实际特征数量: 227，避免使用预定义的FEATURE_COLUMNS(163个)
2025-08-04 22:15:25,489 - WARNING - 特征数量过多: 227，可能导致过拟合
2025-08-04 22:15:25,490 - INFO - 🔧 首板策略智能映射：输入227维 -> 映射到128维
2025-08-04 22:15:26,591 - INFO - regression_output_1稳健标准化后：均值=-0.0508，中位数=0.0000，标准差=0.4143，最小值=-0.8793，最大值=0.4510
2025-08-04 22:15:26,592 - INFO - regression_output_2稳健标准化后：均值=-0.0508，中位数=0.0000，标准差=0.4143，最小值=-0.8793，最大值=0.4510
2025-08-04 22:15:26,593 - INFO - 调整regression_output_1的IQR到最小值: 8.0
2025-08-04 22:15:26,593 - INFO - regression_output_1稳健标准化后：均值=-0.0063，中位数=0.0000，标准差=0.0518，最小值=-0.1099，最大值=0.0564
2025-08-04 22:15:26,594 - INFO - 调整regression_output_2的IQR到最小值: 8.0
2025-08-04 22:15:26,594 - INFO - regression_output_2稳健标准化后：均值=-0.0063，中位数=0.0000，标准差=0.0518，最小值=-0.1099，最大值=0.0564
2025-08-04 22:15:26,594 - INFO - 保持regression_output_1的IQR值: 10.0000
2025-08-04 22:15:26,595 - INFO - regression_output_1稳健标准化后：均值=-0.0196，中位数=0.0000，标准差=0.2437，最小值=-0.5000，最大值=0.5000
2025-08-04 22:15:26,596 - INFO - 保持regression_output_2的IQR值: 10.0000
2025-08-04 22:15:26,596 - INFO - regression_output_2稳健标准化后：均值=-0.0196，中位数=0.0000，标准差=0.2437，最小值=-0.5000，最大值=0.5000
2025-08-04 22:15:26,596 - INFO - ✅ 已将字典格式标签转换为列表格式，匹配模型输出顺序
1/2 [==============>...............] - ETA: 19s - loss: 4.8998 - classification_output_1_loss: 0.2190 - regression_output_1_loss: 0.8995 - classification_output_2_loss: 0.2469 - regression_output_2_loss: 0.9506 - classification_output_1_binary_accuracy: 0.4375 - classification_output_1_classification_output_1_auc: 0.5636 - regression_output_1_mse: 0.7136 - classification_output_2_binary_accuracy: 0.4766 - classification_output_2_classification_output_2_auc: 0.4185 - regression_o2/2 [==============================] - ETA: 0s - loss: 4.8364 - classification_output_1_loss: 0.2158 - regression_output_1_loss: 0.8750 - classification_output_2_loss: 0.2437 - regression_output_2_loss: 0.9591 - classification_output_1_binary_accuracy: 0.4211 - classification_output_1_classification_output_1_auc: 0.5468 - regression_output_1_mse: 0.6906 - classification_output_2_binary_accuracy: 0.4812 - classification_output_2_classification_output_2_auc: 0.4315 - regression_output_2_mse: 0.7534 
Epoch 1: val_loss improved from inf to 3.47210, saving model to models/首板_model_v47_20250804_tf
INFO:tensorflow:Assets written to: models/首板_model_v47_20250804_tf/assets
2025-08-04 22:16:05,597 - INFO - Assets written to: models/首板_model_v47_20250804_tf/assets
2/2 [==============================] - 39s 19s/step - loss: 4.8364 - classification_output_1_loss: 0.2158 - regression_output_1_loss: 0.8750 - classification_output_2_loss: 0.2437 - regression_output_2_loss: 0.9591 - classification_output_1_binary_accuracy: 0.4211 - classification_output_1_classification_output_1_auc: 0.5468 - regression_output_1_mse: 0.6906 - classification_output_2_binary_accuracy: 0.4812 - classification_output_2_classification_output_2_auc: 0.4315 - regression_output_2_mse: 0.7534 - val_loss: 3.4721 - val_classification_output_1_loss: 0.1949 - val_regression_output_1_loss: 0.4142 - val_classification_output_2_loss: 0.2056 - val_regression_output_2_loss: 0.6970 - val_classification_output_1_binary_accuracy: 0.6778 - val_classification_output_1_classification_output_1_auc: 0.5319 - val_regression_output_1_mse: 0.2325 - val_classification_output_2_binary_accuracy: 0.6183 - val_classification_output_2_classification_output_2_auc: 0.6112 - val_regression_output_2_mse: 0.5815 - lr: 5.0000e-04 - gpu_mem: 0.0000e+00 - cpu_mem: 2770.3125
2025-08-04 22:16:05,780 - INFO - 策略 '首板' 训练完成。 最后一轮: 验证损失=3.4721, 验证AUC1=nan
INFO:tensorflow:Assets written to: models/首板_model_v47_20250804_221605_izzuntrr/assets
2025-08-04 22:16:20,078 - INFO - Assets written to: models/首板_model_v47_20250804_221605_izzuntrr/assets
2025-08-04 22:16:20,278 - INFO - 已保存模型: models/首板_model_v47_20250804_221605_izzuntrr
2025-08-04 22:16:26,393 - INFO - 完成 首板 策略训练，清理内存...
2025-08-04 22:16:27,079 - INFO - 
=== 开始 连板 策略训练 ===
2025-08-04 22:16:27,685 - INFO - 🔒 开始安全准备连板策略数据（无数据泄漏版本）
2025-08-04 22:16:29,214 - INFO - ✅ trade_date列已标准化为YYYY-MM-DD格式
2025-08-04 22:16:29,813 - INFO - ✅ 过滤后数据集: 328727行, 开始日期: 2025-01-01
2025-08-04 22:16:29,813 - INFO - 🔒 步骤2: 严格按时间进行数据分割（防止数据泄漏）
2025-08-04 22:16:30,798 - INFO - 🔒 时间分割结果:
2025-08-04 22:16:30,798 - INFO -   训练集: 94372行 (2025-06-04之前)
2025-08-04 22:16:30,798 - INFO -   验证集: 113750行 (2025-06-04~2025-07-03)
2025-08-04 22:16:30,798 - INFO -   测试集: 120605行 (2025-07-03之后)
2025-08-04 22:16:30,799 - INFO - 🔒 步骤3: 只基于训练集计算安全特征
2025-08-04 22:16:30,945 - INFO - 🔒 步骤4: 为连板策略安全生成目标变量
2025-08-04 22:16:31,418 - WARNING - ⚠️ 验证集/测试集中有5409个future_1_day_pct_chg为NaN，这是正常的边界情况
2025-08-04 22:16:31,429 - INFO -   ⚠️ 注意：验证集/测试集的目标变量仅用于模型评估，不用于特征工程
2025-08-04 22:16:31,755 - WARNING - ⚠️ 验证集/测试集中有5418个future_1_day_pct_chg为NaN，这是正常的边界情况
2025-08-04 22:16:31,767 - INFO -   ⚠️ 注意：验证集/测试集的目标变量仅用于模型评估，不用于特征工程
2025-08-04 22:16:31,767 - INFO - 🔒 处理连板策略（安全版本）...
2025-08-04 22:16:41,410 - INFO -   连板增强样本生成：从1795个基础样本生成了1256个增强样本
2025-08-04 22:17:00,459 - INFO -   连板增强样本生成：从3553个基础样本生成了3591个增强样本
2025-08-04 22:17:28,330 - INFO -   连板增强样本生成：从5124个基础样本生成了6130个增强样本
2025-08-04 22:17:28,425 - INFO - ✅ 训练集中找到1256个有效连板样本
2025-08-04 22:17:28,426 - INFO - ✅ 验证集中找到3591个连板样本
2025-08-04 22:17:28,428 - INFO - ✅ 测试集中找到6130个连板样本
2025-08-04 22:17:28,434 - INFO - 🔒 步骤5: 准备最终的训练数据
2025-08-04 22:17:28,434 - INFO - ✅ 连板策略总样本数: 10977
2025-08-04 22:17:28,434 - INFO - 🔒 步骤6: 准备序列数据（时间序列格式）
2025-08-04 22:17:28,450 - INFO - 🔧 发现235个潜在特征列
2025-08-04 22:17:28,495 - WARNING - ⚠️ 跳过特征buy_sm_amount_mf：有效数据不足(0/10977)
2025-08-04 22:17:28,495 - WARNING - ⚠️ 跳过特征sell_sm_amount_mf：有效数据不足(0/10977)
2025-08-04 22:17:28,496 - WARNING - ⚠️ 跳过特征buy_md_amount_mf：有效数据不足(0/10977)
2025-08-04 22:17:28,496 - WARNING - ⚠️ 跳过特征sell_md_amount_mf：有效数据不足(0/10977)
2025-08-04 22:17:28,496 - WARNING - ⚠️ 跳过特征buy_lg_amount_mf：有效数据不足(0/10977)
2025-08-04 22:17:28,496 - WARNING - ⚠️ 跳过特征sell_lg_amount_mf：有效数据不足(0/10977)
2025-08-04 22:17:28,497 - WARNING - ⚠️ 跳过特征buy_elg_amount_mf：有效数据不足(0/10977)
2025-08-04 22:17:28,497 - WARNING - ⚠️ 跳过特征sell_elg_amount_mf：有效数据不足(0/10977)
2025-08-04 22:17:28,498 - WARNING - ⚠️ 跳过特征net_mf_amount_mf：有效数据不足(0/10977)
2025-08-04 22:17:28,502 - INFO - ✅ 筛选出226个有效特征
2025-08-04 22:17:28,503 - INFO - ✅ 使用pe_ttm作为pe特征
2025-08-04 22:17:28,503 - INFO - ✅ 使用228个有效特征
2025-08-04 22:17:28,503 - INFO - 🔧 开始生成序列数据，输入数据: 10977行, 序列长度: 10
2025-08-04 22:17:28,503 - INFO - 🔧 有效特征数量: 228
2025-08-04 22:17:34,146 - INFO - 🔒 步骤7: 执行最终的数据分割和返回
2025-08-04 22:17:34,147 - INFO - 🔧 数组长度统计: X=5684, y1_cls=5684, y1_reg=5684, y2_cls=5684, y2_reg=5684, weights=5684, 最小长度=5684
2025-08-04 22:17:34,148 - INFO - ✅ 所有数组长度已统一为: 5684
2025-08-04 22:17:34,175 - INFO - ✅ 最终数据形状: X=(5684, 10, 228), 样本权重=5684
2025-08-04 22:17:34,175 - INFO - 🔧 开始正确的时间序列分割...
2025-08-04 22:17:34,176 - INFO - 🔧 时间序列分割点:
2025-08-04 22:17:34,176 - INFO -   训练集: 2025-01-03 到 2025-06-30
2025-08-04 22:17:34,176 - INFO -   验证集: 2025-07-01 到 2025-07-14
2025-08-04 22:17:34,176 - INFO -   测试集: 2025-07-15 到 2025-07-30
2025-08-04 22:17:40,728 - INFO - 🔒 三分法安全分割完成:
2025-08-04 22:17:40,728 - INFO -   训练集: (1887, 10, 228)
2025-08-04 22:17:40,728 - INFO -   验证集: (1405, 10, 228)
2025-08-04 22:17:40,728 - INFO -   测试集: (2392, 10, 228)
2025-08-04 22:17:40,728 - INFO - 
📊 训练集详细样本统计 (连板策略):
2025-08-04 22:17:40,728 - INFO -   总样本数: 1887
2025-08-04 22:17:40,728 - INFO -   分类输出1:
2025-08-04 22:17:40,728 - INFO -     负样本(值=0): 1562个 (82.8%)
2025-08-04 22:17:40,728 - INFO -     正样本(值=1): 325个 (17.2%)
2025-08-04 22:17:40,728 - INFO -     🔴 样本严重不平衡 (平衡度: 0.21)
2025-08-04 22:17:40,729 - INFO -   回归输出1:
2025-08-04 22:17:40,729 - INFO -     有效样本: 1887个 (缺失: 0个)
2025-08-04 22:17:40,729 - INFO -     均值: 0.482%
2025-08-04 22:17:40,729 - INFO -     标准差: 5.914%
2025-08-04 22:17:40,729 - INFO -     范围: [-10.480%, 10.996%]
2025-08-04 22:17:40,729 - INFO -     中位数: -0.090%
2025-08-04 22:17:40,730 - INFO -   分类输出2:
2025-08-04 22:17:40,730 - INFO -     负样本(值=0): 1562个 (82.8%)
2025-08-04 22:17:40,730 - INFO -     正样本(值=1): 325个 (17.2%)
2025-08-04 22:17:40,730 - INFO -     🔴 样本严重不平衡 (平衡度: 0.21)
2025-08-04 22:17:40,730 - INFO -   回归输出2:
2025-08-04 22:17:40,730 - INFO -     有效样本: 1887个 (缺失: 0个)
2025-08-04 22:17:40,730 - INFO -     均值: 0.482%
2025-08-04 22:17:40,730 - INFO -     标准差: 5.914%
2025-08-04 22:17:40,731 - INFO -     范围: [-10.480%, 10.996%]
2025-08-04 22:17:40,731 - INFO -     中位数: -0.090%
2025-08-04 22:17:40,731 - INFO - 
📊 验证集详细样本统计 (连板策略):
2025-08-04 22:17:40,731 - INFO -   总样本数: 1405
2025-08-04 22:17:40,731 - INFO -   分类输出1:
2025-08-04 22:17:40,731 - INFO -     负样本(值=0): 773个 (55.0%)
2025-08-04 22:17:40,731 - INFO -     正样本(值=1): 632个 (45.0%)
2025-08-04 22:17:40,731 - INFO -     ✅ 样本平衡性良好 (平衡度: 0.82)
2025-08-04 22:17:40,731 - INFO -   回归输出1:
2025-08-04 22:17:40,732 - INFO -     有效样本: 1405个 (缺失: 0个)
2025-08-04 22:17:40,732 - INFO -     均值: 3.725%
2025-08-04 22:17:40,732 - INFO -     标准差: 6.611%
2025-08-04 22:17:40,732 - INFO -     范围: [-10.480%, 10.996%]
2025-08-04 22:17:40,732 - INFO -     中位数: 4.589%
2025-08-04 22:17:40,732 - INFO -   分类输出2:
2025-08-04 22:17:40,732 - INFO -     负样本(值=0): 773个 (55.0%)
2025-08-04 22:17:40,732 - INFO -     正样本(值=1): 632个 (45.0%)
2025-08-04 22:17:40,732 - INFO -     ✅ 样本平衡性良好 (平衡度: 0.82)
2025-08-04 22:17:40,733 - INFO -   回归输出2:
2025-08-04 22:17:40,733 - INFO -     有效样本: 1405个 (缺失: 0个)
2025-08-04 22:17:40,733 - INFO -     均值: 3.725%
2025-08-04 22:17:40,733 - INFO -     标准差: 6.611%
2025-08-04 22:17:40,733 - INFO -     范围: [-10.480%, 10.996%]
2025-08-04 22:17:40,733 - INFO -     中位数: 4.589%
2025-08-04 22:17:40,733 - INFO - 
📊 测试集详细样本统计 (连板策略):
2025-08-04 22:17:40,733 - INFO -   总样本数: 2392
2025-08-04 22:17:40,734 - INFO -   分类输出1:
2025-08-04 22:17:40,734 - INFO -     负样本(值=0): 1501个 (62.8%)
2025-08-04 22:17:40,734 - INFO -     正样本(值=1): 891个 (37.2%)
2025-08-04 22:17:40,734 - INFO -     ⚠️ 样本轻度不平衡 (平衡度: 0.59)
2025-08-04 22:17:40,734 - INFO -   回归输出1:
2025-08-04 22:17:40,734 - INFO -     有效样本: 2392个 (缺失: 0个)
2025-08-04 22:17:40,734 - INFO -     均值: 3.158%
2025-08-04 22:17:40,734 - INFO -     标准差: 6.225%
2025-08-04 22:17:40,734 - INFO -     范围: [-10.480%, 10.996%]
2025-08-04 22:17:40,735 - INFO -     中位数: 2.023%
2025-08-04 22:17:40,735 - INFO -   分类输出2:
2025-08-04 22:17:40,735 - INFO -     负样本(值=0): 1501个 (62.8%)
2025-08-04 22:17:40,735 - INFO -     正样本(值=1): 891个 (37.2%)
2025-08-04 22:17:40,735 - INFO -     ⚠️ 样本轻度不平衡 (平衡度: 0.59)
2025-08-04 22:17:40,735 - INFO -   回归输出2:
2025-08-04 22:17:40,735 - INFO -     有效样本: 2392个 (缺失: 0个)
2025-08-04 22:17:40,735 - INFO -     均值: 3.158%
2025-08-04 22:17:40,736 - INFO -     标准差: 6.225%
2025-08-04 22:17:40,736 - INFO -     范围: [-10.480%, 10.996%]
2025-08-04 22:17:40,736 - INFO -     中位数: 2.023%
2025-08-04 22:17:40,736 - INFO - 🔒 检查样本分布质量...
2025-08-04 22:17:40,736 - INFO - 训练集 classification_output_1分布: {0: 1562, 1: 325}
2025-08-04 22:17:40,736 - INFO - 训练集 classification_output_2分布: {0: 1562, 1: 325}
2025-08-04 22:17:40,737 - INFO - 验证集 classification_output_1分布: {0: 773, 1: 632}
2025-08-04 22:17:40,737 - INFO - 验证集 classification_output_2分布: {0: 773, 1: 632}
2025-08-04 22:17:40,737 - INFO - 测试集 classification_output_1分布: {0: 1501, 1: 891}
2025-08-04 22:17:40,737 - INFO - 测试集 classification_output_2分布: {0: 1501, 1: 891}
2025-08-04 22:17:40,737 - INFO - ✅ 样本分布质量检查通过
2025-08-04 22:17:40,738 - INFO -   classification_output_1 - 训练集分布: [1562  325], 验证集分布: [773 632], 测试集分布: [1501  891]
2025-08-04 22:17:40,738 - INFO -   classification_output_2 - 训练集分布: [1562  325], 验证集分布: [773 632], 测试集分布: [1501  891]
2025-08-04 22:17:40,738 - INFO - ✅ 连板策略数据准备完成，耗时: 73.05秒
2025-08-04 22:17:40,827 - INFO - 特征维度: 228个特征，10个时间步
2025-08-04 22:17:40,827 - INFO - ✅ 已将字典格式标签转换为列表格式用于超参数优化
2025-08-04 22:17:40,827 - INFO - 开始连板策略超参数优化...
2025-08-04 22:17:40,830 - INFO - 元学习初始化完成 | 有效记录: 0条
[I 2025-08-04 22:17:40,831] A new study created in memory with name: no-name-af210ab1-73ad-4e1a-a004-b78fbd237d4e
2025-08-04 22:17:40,831 - WARNING - objective函数: y_train是列表格式，转换为字典格式
2025-08-04 22:17:40,831 - WARNING - objective函数: y_test是列表格式，转换为字典格式
2025-08-04 22:17:40,835 - INFO - 专家网络单元数 32 小于 连板 策略建议值 64，但保持超参数优化器的选择
2025-08-04 22:17:40,836 - INFO - 🔧 连板策略智能映射：输入228维 -> 映射到160维
2025-08-04 22:17:42,359 - INFO - 编译模型配置，策略类型: 连板, 学习率: 1.1415338890724785e-05
2025-08-04 22:17:42,360 - INFO - 使用AdEMAMix优化器 - 2024年最新技术，混合双EMA设计
2025-08-04 22:17:42,385 - INFO - 监控指标已设置为动态匹配val_classification_output_1_auc，ReduceLROnPlateau已禁用，学习率由LearningRateScheduler全权控制
2025-08-04 22:17:42,385 - WARNING - y_train不是标准的列表/元组格式，尝试替代方法提取标签
2025-08-04 22:17:42,386 - INFO - regression_output_1稳健标准化后：均值=0.1005，中位数=0.0000，标准差=1.0402，最小值=-1.8274，最大值=1.9498
2025-08-04 22:17:42,386 - INFO - regression_output_1稳健标准化后：均值=0.1005，中位数=0.0000，标准差=1.0402，最小值=-1.8274，最大值=1.9498
2025-08-04 22:17:42,387 - INFO - regression_output_2稳健标准化后：均值=0.1005，中位数=0.0000，标准差=1.0402，最小值=-1.8274，最大值=1.9498
2025-08-04 22:17:42,388 - INFO - regression_output_2稳健标准化后：均值=0.1005，中位数=0.0000，标准差=1.0402，最小值=-1.8274，最大值=1.9498
2025-08-04 22:17:42,388 - INFO - regression_output_1稳健标准化后：均值=0.1293，中位数=0.0000，标准差=0.7096，最小值=-1.4251，最大值=1.0228
2025-08-04 22:17:42,389 - INFO - regression_output_2稳健标准化后：均值=0.1293，中位数=0.0000，标准差=0.7096，最小值=-1.4251，最大值=1.0228
2025-08-04 22:17:42,520 - WARNING - 无法找到与 val_classification_output_1_auc 匹配的指标，使用 val_loss 代替
Epoch 1/30
 1/15 [=>............................] - ETA: 6:11 - loss: 4.8039 - classification_output_1_loss: 0.1290 - regression_output_1_loss: 1.0788 - classification_output_2_loss: 0.1472 - regression_output_2_loss: 1.1156 - classification_output_1_binary_accuracy: 0.3594 - classification_output_1_classification_output_1_auc: 0.7355 - regression_output_1_mse: 0.8405 - classification_output_2_binary_accuracy: 0.7578 - classification_output_2_classification_output_2_auc: 0.6972 - regressio 2/15 [===>..........................] - ETA: 51s - loss: 6.3021 - classification_output_1_loss: 0.1663 - regression_output_1_loss: 1.5101 - classification_output_2_loss: 0.1941 - regression_output_2_loss: 1.4708 - classification_output_1_binary_accuracy: 0.3594 - classification_output_1_classification_output_1_auc: 0.4860 - regression_output_1_mse: 1.4126 - classification_output_2_binary_accuracy: 0.6953 - classification_output_2_classification_output_2_auc: 0.5988 - regression 3/15 [=====>........................] - ETA: 23s - loss: 6.5459 - classification_output_1_loss: 0.1756 - regression_output_1_loss: 1.5783 - classification_output_2_loss: 0.2039 - regression_output_2_loss: 1.5123 - classification_output_1_binary_accuracy: 0.3828 - classification_output_1_classification_output_1_auc: 0.5145 - regression_output_1_mse: 1.4853 - classification_output_2_binary_accuracy: 0.6901 - classification_output_2_classification_output_2_auc: 0.6211 - regression 4/15 [=======>......................] - ETA: 14s - loss: 6.5523 - classification_output_1_loss: 0.1800 - regression_output_1_loss: 1.5778 - classification_output_2_loss: 0.2068 - regression_output_2_loss: 1.4919 - classification_output_1_binary_accuracy: 0.4023 - classification_output_1_classification_output_1_auc: 0.5376 - regression_output_1_mse: 1.4726 - classification_output_2_binary_accuracy: 0.6797 - classification_output_2_classification_output_2_auc: 0.6115 - regression 5/15 [=========>....................] - ETA: 10s - loss: 6.7250 - classification_output_1_loss: 0.1851 - regression_output_1_loss: 1.6325 - classification_output_2_loss: 0.2141 - regression_output_2_loss: 1.5153 - classification_output_1_binary_accuracy: 0.4094 - classification_output_1_classification_output_1_auc: 0.5347 - regression_output_1_mse: 1.5211 - classification_output_2_binary_accuracy: 0.6562 - classification_output_2_classification_output_2_auc: 0.5531 - regression 6/15 [===========>..................] - ETA: 7s - loss: 6.6989 - classification_output_1_loss: 0.1851 - regression_output_1_loss: 1.6180 - classification_output_2_loss: 0.2145 - regression_output_2_loss: 1.5175 - classification_output_1_binary_accuracy: 0.4128 - classification_output_1_classification_output_1_auc: 0.5348 - regression_output_1_mse: 1.5107 - classification_output_2_binary_accuracy: 0.6654 - classification_output_2_classification_output_2_auc: 0.5527 - regression_ 7/15 [=============>................] - ETA: 5s - loss: 6.7229 - classification_output_1_loss: 0.1852 - regression_output_1_loss: 1.6346 - classification_output_2_loss: 0.2142 - regression_output_2_loss: 1.5080 - classification_output_1_binary_accuracy: 0.4085 - classification_output_1_classification_output_1_auc: 0.5234 - regression_output_1_mse: 1.5006 - classification_output_2_binary_accuracy: 0.6674 - classification_output_2_classification_output_2_auc: 0.5453 - regression_ 8/15 [===============>..............] - ETA: 4s - loss: 6.6927 - classification_output_1_loss: 0.1831 - regression_output_1_loss: 1.6309 - classification_output_2_loss: 0.2119 - regression_output_2_loss: 1.5007 - classification_output_1_binary_accuracy: 0.4062 - classification_output_1_classification_output_1_auc: 0.5206 - regression_output_1_mse: 1.5064 - classification_output_2_binary_accuracy: 0.6699 - classification_output_2_classification_output_2_auc: 0.5409 - regression_ 9/15 [=================>............] - ETA: 3s - loss: 6.6616 - classification_output_1_loss: 0.1810 - regression_output_1_loss: 1.6266 - classification_output_2_loss: 0.2091 - regression_output_2_loss: 1.4939 - classification_output_1_binary_accuracy: 0.4045 - classification_output_1_classification_output_1_auc: 0.5223 - regression_output_1_mse: 1.4931 - classification_output_2_binary_accuracy: 0.6693 - classification_output_2_classification_output_2_auc: 0.5384 - regression_10/15 [===================>..........] - ETA: 2s - loss: 6.5994 - classification_output_1_loss: 0.1797 - regression_output_1_loss: 1.6121 - classification_output_2_loss: 0.2080 - regression_output_2_loss: 1.4696 - classification_output_1_binary_accuracy: 0.4078 - classification_output_1_classification_output_1_auc: 0.5312 - regression_output_1_mse: 1.4744 - classification_output_2_binary_accuracy: 0.6672 - classification_output_2_classification_output_2_auc: 0.5346 - regression_11/15 [=====================>........] - ETA: 1s - loss: 6.5501 - classification_output_1_loss: 0.1782 - regression_output_1_loss: 1.5960 - classification_output_2_loss: 0.2066 - regression_output_2_loss: 1.4628 - classification_output_1_binary_accuracy: 0.4055 - classification_output_1_classification_output_1_auc: 0.5294 - regression_output_1_mse: 1.4509 - classification_output_2_binary_accuracy: 0.6669 - classification_output_2_classification_output_2_auc: 0.5215 - regression_12/15 [=======================>......] - ETA: 1s - loss: 6.3981 - classification_output_1_loss: 0.1741 - regression_output_1_loss: 1.5534 - classification_output_2_loss: 0.2017 - regression_output_2_loss: 1.4264 - classification_output_1_binary_accuracy: 0.3958 - classification_output_1_classification_output_1_auc: 0.5192 - regression_output_1_mse: 1.3959 - classification_output_2_binary_accuracy: 0.6660 - classification_output_2_classification_output_2_auc: 0.5201 - regression_13/15 [=========================>....] - ETA: 0s - loss: 6.4112 - classification_output_1_loss: 0.1739 - regression_output_1_loss: 1.5636 - classification_output_2_loss: 0.2015 - regression_output_2_loss: 1.4204 - classification_output_1_binary_accuracy: 0.3972 - classification_output_1_classification_output_1_auc: 0.5237 - regression_output_1_mse: 1.4003 - classification_output_2_binary_accuracy: 0.6701 - classification_output_2_classification_output_2_auc: 0.5309 - regression_14/15 [===========================>..] - ETA: 0s - loss: 6.2361 - classification_output_1_loss: 0.1707 - regression_output_1_loss: 1.5114 - classification_output_2_loss: 0.1966 - regression_output_2_loss: 1.3756 - classification_output_1_binary_accuracy: 0.3956 - classification_output_1_classification_output_1_auc: 0.5252 - regression_output_1_mse: 1.3399 - classification_output_2_binary_accuracy: 0.6724 - classification_output_2_classification_output_2_auc: 0.5290 - regression_15/15 [==============================] - ETA: 0s - loss: 6.1841 - classification_output_1_loss: 0.1688 - regression_output_1_loss: 1.4948 - classification_output_2_loss: 0.1942 - regression_output_2_loss: 1.3706 - classification_output_1_binary_accuracy: 0.3948 - classification_output_1_classification_output_1_auc: 0.5260 - regression_output_1_mse: 1.3162 - classification_output_2_binary_accuracy: 0.6778 - classification_output_2_classification_output_2_auc: 0.5328 - regression_output_2_mse: 1.1617[W 2025-08-04 22:18:16,530] Trial 0 failed with parameters: {'param_strategy': 'history', 'lstm_units_1': 192, 'batch_size': 64, 'learning_rate': 0.01220776478695415, 'dropout_rate': 0.30000000000000004, 'l2_reg': 0.0006796578090758161, 'patience': 5, 'attention_heads': 12, 'num_experts_1': 8, 'expert_units_1': 32, 'num_experts_2': 2, 'expert_units_2': 16, 'combined_weight': 0.3042422429595377, 'base_learning_rate': 3.752055855124284e-05, 'kt_num_experts': 4, 'kt_expert_units': 60} because of the following error: ValueError('Unable to synchronously create dataset (name already exists)').
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/optuna/study/_optimize.py", line 201, in _run_trial
    value_or_values = func(trial)
  File "/home/<USER>/P.pull.py", line 5540, in objective
    history = model.fit(
  File "/home/<USER>/.local/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/.local/lib/python3.10/site-packages/h5py/_hl/group.py", line 186, in create_dataset
    dsid = dataset.make_new_dset(group, shape, dtype, data, name, **kwds)
  File "/home/<USER>/.local/lib/python3.10/site-packages/h5py/_hl/dataset.py", line 175, in make_new_dset
    dset_id = h5d.create(parent.id, name, tid, sid, dcpl=dcpl, dapl=dapl)
  File "h5py/_objects.pyx", line 56, in h5py._objects.with_phil.wrapper
  File "h5py/_objects.pyx", line 57, in h5py._objects.with_phil.wrapper
  File "h5py/h5d.pyx", line 136, in h5py.h5d.create
ValueError: Unable to synchronously create dataset (name already exists)
[W 2025-08-04 22:18:16,530] Trial 0 failed with value None.
2025-08-04 22:18:17,337 - ERROR - 超参数优化失败: Unable to synchronously create dataset (name already exists)
2025-08-04 22:18:17,346 - INFO - 已更新连板策略元学习历史记录，当前记录数: 1
2025-08-04 22:18:17,346 - ERROR - 获取最佳模型失败: No trials are completed yet.
2025-08-04 22:18:17,346 - ERROR - 清理试验资源时出错: No trials are completed yet.
2025-08-04 22:18:17,346 - INFO - 优化完成，最佳验证损失: inf
2025-08-04 22:18:17,346 - INFO - 超参数优化耗时: 36.52秒
2025-08-04 22:18:17,347 - INFO - 最佳参数已保存到 models/连板_best_params.pkl
2025-08-04 22:18:17,347 - INFO - 连板策略最佳超参数（完整）:
2025-08-04 22:18:17,347 - INFO -   lstm_units_1: 256
2025-08-04 22:18:17,347 - INFO -   lstm_units_2: 64
2025-08-04 22:18:17,347 - INFO -   attention_heads: 12
2025-08-04 22:18:17,347 - INFO -   dropout_rate: 0.3
2025-08-04 22:18:17,347 - INFO -   l2_reg: 0.001
2025-08-04 22:18:17,347 - INFO -   learning_rate: 0.0005
2025-08-04 22:18:17,347 - INFO -   batch_size: 128
2025-08-04 22:18:17,347 - INFO -   patience: 10
2025-08-04 22:18:17,347 - INFO -   num_experts_1: 6
2025-08-04 22:18:17,347 - INFO -   expert_units_1: 96
2025-08-04 22:18:17,347 - INFO -   num_experts_2: 2
2025-08-04 22:18:17,347 - INFO -   expert_units_2: 32
2025-08-04 22:18:17,347 - INFO - 连板策略最佳超参数JSON格式: {
  "lstm_units_1": 256,
  "lstm_units_2": 64,
  "attention_heads": 12,
  "dropout_rate": 0.3,
  "l2_reg": 0.001,
  "learning_rate": 0.0005,
  "batch_size": 128,
  "patience": 10,
  "num_experts_1": 6,
  "expert_units_1": 96,
  "num_experts_2": 2,
  "expert_units_2": 32
}
2025-08-04 22:18:17,347 - INFO - 连板策略最佳超参数: {'lstm_units_1': 256, 'lstm_units_2': 64, 'attention_heads': 12, 'dropout_rate': 0.3, 'l2_reg': 0.001, 'learning_rate': 0.0005, 'batch_size': 128, 'patience': 10, 'num_experts_1': 6, 'expert_units_1': 96, 'num_experts_2': 2, 'expert_units_2': 32}
2025-08-04 22:18:17,348 - INFO - 开始连板策略模型部署...
2025-08-04 22:18:17,348 - INFO - 超参数优化过程未返回模型，构建新模型...
2025-08-04 22:18:17,348 - INFO - 🔧 使用实际特征数量: 228，避免使用预定义的FEATURE_COLUMNS(163个)
2025-08-04 22:18:17,348 - WARNING - 特征数量过多: 228，可能导致过拟合
2025-08-04 22:18:17,349 - INFO - 🔧 连板策略智能映射：输入228维 -> 映射到160维
2025-08-04 22:18:18,704 - INFO - regression_output_1稳健标准化后：均值=0.0515，中位数=0.0000，标准差=0.5327，最小值=-0.9357，最大值=0.9984
2025-08-04 22:18:18,705 - INFO - regression_output_2稳健标准化后：均值=0.0515，中位数=0.0000，标准差=0.5327，最小值=-0.9357，最大值=0.9984
2025-08-04 22:18:18,705 - INFO - 调整regression_output_1的IQR到最小值: 8.0
2025-08-04 22:18:18,706 - INFO - regression_output_1稳健标准化后：均值=0.0064，中位数=0.0000，标准差=0.0666，最小值=-0.1170，最大值=0.1248
2025-08-04 22:18:18,706 - INFO - 调整regression_output_2的IQR到最小值: 8.0
2025-08-04 22:18:18,707 - INFO - regression_output_2稳健标准化后：均值=0.0064，中位数=0.0000，标准差=0.0666，最小值=-0.1170，最大值=0.1248
2025-08-04 22:18:18,707 - INFO - 调整regression_output_1的IQR到最小值: 8.0
2025-08-04 22:18:18,708 - INFO - regression_output_1稳健标准化后：均值=0.0091，中位数=0.0000，标准差=0.0501，最小值=-0.1006，最大值=0.0722
2025-08-04 22:18:18,709 - INFO - 调整regression_output_2的IQR到最小值: 8.0
2025-08-04 22:18:18,709 - INFO - regression_output_2稳健标准化后：均值=0.0091，中位数=0.0000，标准差=0.0501，最小值=-0.1006，最大值=0.0722
2025-08-04 22:18:18,709 - INFO - ✅ 已将字典格式标签转换为列表格式，匹配模型输出顺序
 1/15 [=>............................] - ETA: 5:10 - loss: 4.3153 - classification_output_1_loss: 0.2000 - regression_output_1_loss: 0.6850 - classification_output_2_loss: 0.2109 - regression_output_2_loss: 0.4984 - classification_output_1_binary_accuracy: 0.6250 - classification_output_1_classification_output_1_auc: 0.6431 - regression_output_1_mse: 0.3355 - classification_output_2_binary_accuracy: 0.5938 - classification_output_2_classification_output_2_auc: 0.4917 - regressio 2/15 [===>..........................] - ETA: 42s - loss: 3.9621 - classification_output_1_loss: 0.1802 - regression_output_1_loss: 0.5956 - classification_output_2_loss: 0.1860 - regression_output_2_loss: 0.4726 - classification_output_1_binary_accuracy: 0.6250 - classification_output_1_classification_output_1_auc: 0.6391 - regression_output_1_mse: 0.2789 - classification_output_2_binary_accuracy: 0.5938 - classification_output_2_classification_output_2_auc: 0.4897 - regression 3/15 [=====>........................] - ETA: 20s - loss: 3.9622 - classification_output_1_loss: 0.1840 - regression_output_1_loss: 0.5867 - classification_output_2_loss: 0.1867 - regression_output_2_loss: 0.4705 - classification_output_1_binary_accuracy: 0.6198 - classification_output_1_classification_output_1_auc: 0.5835 - regression_output_1_mse: 0.2806 - classification_output_2_binary_accuracy: 0.5677 - classification_output_2_classification_output_2_auc: 0.4647 - regression 4/15 [=======>......................] - ETA: 12s - loss: 3.8957 - classification_output_1_loss: 0.1857 - regression_output_1_loss: 0.5537 - classification_output_2_loss: 0.1869 - regression_output_2_loss: 0.4613 - classification_output_1_binary_accuracy: 0.6133 - classification_output_1_classification_output_1_auc: 0.5690 - regression_output_1_mse: 0.2596 - classification_output_2_binary_accuracy: 0.5586 - classification_output_2_classification_output_2_auc: 0.4482 - regression 5/15 [=========>....................] - ETA: 8s - loss: 3.8141 - classification_output_1_loss: 0.1860 - regression_output_1_loss: 0.5242 - classification_output_2_loss: 0.1877 - regression_output_2_loss: 0.4358 - classification_output_1_binary_accuracy: 0.6250 - classification_output_1_classification_output_1_auc: 0.5863 - regression_output_1_mse: 0.2380 - classification_output_2_binary_accuracy: 0.5594 - classification_output_2_classification_output_2_auc: 0.4502 - regression_ 6/15 [===========>..................] - ETA: 6s - loss: 3.8037 - classification_output_1_loss: 0.1855 - regression_output_1_loss: 0.5254 - classification_output_2_loss: 0.1870 - regression_output_2_loss: 0.4269 - classification_output_1_binary_accuracy: 0.6250 - classification_output_1_classification_output_1_auc: 0.5738 - regression_output_1_mse: 0.2334 - classification_output_2_binary_accuracy: 0.5638 - classification_output_2_classification_output_2_auc: 0.4523 - regression_ 7/15 [=============>................] - ETA: 4s - loss: 3.7611 - classification_output_1_loss: 0.1881 - regression_output_1_loss: 0.5034 - classification_output_2_loss: 0.1879 - regression_output_2_loss: 0.4140 - classification_output_1_binary_accuracy: 0.6295 - classification_output_1_classification_output_1_auc: 0.5757 - regression_output_1_mse: 0.2192 - classification_output_2_binary_accuracy: 0.5513 - classification_output_2_classification_output_2_auc: 0.4325 - regression_ 8/15 [===============>..............] - ETA: 3s - loss: 3.7269 - classification_output_1_loss: 0.1892 - regression_output_1_loss: 0.4868 - classification_output_2_loss: 0.1893 - regression_output_2_loss: 0.4049 - classification_output_1_binary_accuracy: 0.6260 - classification_output_1_classification_output_1_auc: 0.5582 - regression_output_1_mse: 0.2075 - classification_output_2_binary_accuracy: 0.5527 - classification_output_2_classification_output_2_auc: 0.4284 - regression_ 9/15 [=================>............] - ETA: 2s - loss: 3.7086 - classification_output_1_loss: 0.1909 - regression_output_1_loss: 0.4735 - classification_output_2_loss: 0.1913 - regression_output_2_loss: 0.4004 - classification_output_1_binary_accuracy: 0.6293 - classification_output_1_classification_output_1_auc: 0.5644 - regression_output_1_mse: 0.1982 - classification_output_2_binary_accuracy: 0.5460 - classification_output_2_classification_output_2_auc: 0.4153 - regression_10/15 [===================>..........] - ETA: 2s - loss: 3.6596 - classification_output_1_loss: 0.1898 - regression_output_1_loss: 0.4572 - classification_output_2_loss: 0.1897 - regression_output_2_loss: 0.3933 - classification_output_1_binary_accuracy: 0.6352 - classification_output_1_classification_output_1_auc: 0.5673 - regression_output_1_mse: 0.1879 - classification_output_2_binary_accuracy: 0.5437 - classification_output_2_classification_output_2_auc: 0.4159 - regression_11/15 [=====================>........] - ETA: 1s - loss: 3.6157 - classification_output_1_loss: 0.1905 - regression_output_1_loss: 0.4380 - classification_output_2_loss: 0.1891 - regression_output_2_loss: 0.3855 - classification_output_1_binary_accuracy: 0.6420 - classification_output_1_classification_output_1_auc: 0.5592 - regression_output_1_mse: 0.1764 - classification_output_2_binary_accuracy: 0.5426 - classification_output_2_classification_output_2_auc: 0.4133 - regression_12/15 [=======================>......] - ETA: 1s - loss: 3.5927 - classification_output_1_loss: 0.1924 - regression_output_1_loss: 0.4253 - classification_output_2_loss: 0.1898 - regression_output_2_loss: 0.3774 - classification_output_1_binary_accuracy: 0.6491 - classification_output_1_classification_output_1_auc: 0.5482 - regression_output_1_mse: 0.1680 - classification_output_2_binary_accuracy: 0.5488 - classification_output_2_classification_output_2_auc: 0.4167 - regression_13/15 [=========================>....] - ETA: 0s - loss: 3.5865 - classification_output_1_loss: 0.1946 - regression_output_1_loss: 0.4165 - classification_output_2_loss: 0.1910 - regression_output_2_loss: 0.3757 - classification_output_1_binary_accuracy: 0.6520 - classification_output_1_classification_output_1_auc: 0.5439 - regression_output_1_mse: 0.1619 - classification_output_2_binary_accuracy: 0.5505 - classification_output_2_classification_output_2_auc: 0.4256 - regression_14/15 [===========================>..] - ETA: 0s - loss: 3.5617 - classification_output_1_loss: 0.1952 - regression_output_1_loss: 0.4061 - classification_output_2_loss: 0.1918 - regression_output_2_loss: 0.3669 - classification_output_1_binary_accuracy: 0.6579 - classification_output_1_classification_output_1_auc: 0.5446 - regression_output_1_mse: 0.1558 - classification_output_2_binary_accuracy: 0.5569 - classification_output_2_classification_output_2_auc: 0.4304 - regression_15/15 [==============================] - ETA: 0s - loss: 3.5344 - classification_output_1_loss: 0.1945 - regression_output_1_loss: 0.3982 - classification_output_2_loss: 0.1909 - regression_output_2_loss: 0.3612 - classification_output_1_binary_accuracy: 0.6598 - classification_output_1_classification_output_1_auc: 0.5435 - regression_output_1_mse: 0.1514 - classification_output_2_binary_accuracy: 0.5612 - classification_output_2_classification_output_2_auc: 0.4380 - regression_output_2_mse: 0.1274
Epoch 1: val_loss improved from inf to 3.03662, saving model to models/连板_model_v42_20250804_tf
INFO:tensorflow:Assets written to: models/连板_model_v42_20250804_tf/assets
2025-08-04 22:19:04,502 - INFO - Assets written to: models/连板_model_v42_20250804_tf/assets
15/15 [==============================] - 46s 2s/step - loss: 3.5344 - classification_output_1_loss: 0.1945 - regression_output_1_loss: 0.3982 - classification_output_2_loss: 0.1909 - regression_output_2_loss: 0.3612 - classification_output_1_binary_accuracy: 0.6598 - classification_output_1_classification_output_1_auc: 0.5435 - regression_output_1_mse: 0.1514 - classification_output_2_binary_accuracy: 0.5612 - classification_output_2_classification_output_2_auc: 0.4380 - regression_output_2_mse: 0.1274 - val_loss: 3.0366 - val_classification_output_1_loss: 0.2332 - val_regression_output_1_loss: 0.1414 - val_classification_output_2_loss: 0.2179 - val_regression_output_2_loss: 0.1332 - val_classification_output_1_binary_accuracy: 0.6367 - val_classification_output_1_classification_output_1_auc: 0.6406 - val_regression_output_1_mse: 0.0304 - val_classification_output_2_binary_accuracy: 0.4900 - val_classification_output_2_classification_output_2_auc: 0.4512 - val_regression_output_2_mse: 0.0264 - lr: 5.0000e-04 - gpu_mem: 0.0000e+00 - cpu_mem: 3687.1875
2025-08-04 22:19:04,733 - INFO - 策略 '连板' 训练完成。 最后一轮: 验证损失=3.0366, 验证AUC1=nan
INFO:tensorflow:Assets written to: models/连板_model_v42_20250804_221904_s744vlwa/assets
2025-08-04 22:19:21,460 - INFO - Assets written to: models/连板_model_v42_20250804_221904_s744vlwa/assets
2025-08-04 22:19:21,693 - INFO - 已保存模型: models/连板_model_v42_20250804_221904_s744vlwa
2025-08-04 22:19:29,098 - INFO - 完成 连板 策略训练，清理内存...
2025-08-04 22:19:30,216 - INFO - 模型训练总耗时: 402.71秒
2025-08-04 22:19:37,410 - INFO - 内存清理完成:
清理前: 3873.6MB
清理后: 3617.3MB
释放: 256.3MB
2025-08-04 22:19:37,410 - INFO - 当前模型版本：首板v0 (2024-01-01)，连板v0 (2024-01-01)
2025-08-04 22:19:37,410 - INFO - 训练完成，分析模型文件状态...
2025-08-04 22:19:37,410 - INFO - === 首板策略模型分析 ===
2025-08-04 22:19:37,410 - INFO - 总模型数: 2
2025-08-04 22:19:37,410 - INFO - 计划保留的最佳模型: 2
2025-08-04 22:19:37,410 - INFO - 可清理的模型数: 0
2025-08-04 22:19:37,410 - INFO - 保留的最佳模型:
2025-08-04 22:19:37,411 - INFO -   1. 首板 (mode-l- v4:7:) (性能分数: 0.0000)
2025-08-04 22:19:37,411 - INFO -   2. 首板 (mode-l- v4:7:) (性能分数: 0.0000)
2025-08-04 22:19:37,411 - INFO - === 连板策略模型分析 ===
2025-08-04 22:19:37,411 - INFO - 总模型数: 2
2025-08-04 22:19:37,411 - INFO - 计划保留的最佳模型: 2
2025-08-04 22:19:37,411 - INFO - 可清理的模型数: 0
2025-08-04 22:19:37,411 - INFO - 保留的最佳模型:
2025-08-04 22:19:37,411 - INFO -   1. 连板 (mode-l- v4:2:) (性能分数: 0.0000)
2025-08-04 22:19:37,411 - INFO -   2. 连板 (mode-l- v4:2:) (性能分数: 0.0000)
2025-08-04 22:19:37,411 - INFO - 开始模型预测...
2025-08-04 22:19:37,411 - INFO - 开始首板策略选股...
2025-08-04 22:19:37,412 - INFO - 模型输入维度: (batch_size, 10, 227)
2025-08-04 22:19:37,412 - WARNING - 可用特征数(163)少于期望数量(227)，使用所有可用特征
2025-08-04 22:19:37,412 - INFO - 使用特征数量: 163, 期望特征数: 227
2025-08-04 22:19:37,413 - INFO - 实际使用的特征: ['turnover_rate', 'market_code', 'max_pct', 'turnover_rate_ma5', 'turnover_rate_ma10']...
2025-08-04 22:19:37,433 - INFO - 首板策略初始股票池:
2025-08-04 22:19:37,433 - INFO - 股票数量: 5173
2025-08-04 22:19:37,433 - INFO - 样本股票:
2025-08-04 22:19:37,434 - INFO - - 越秀资本(000987.SZ): 0.68%
2025-08-04 22:19:37,434 - INFO - - 慧辰股份(688500.SH): 2.14%
2025-08-04 22:19:37,434 - INFO - - 冠中生态(300948.SZ): 1.38%
2025-08-04 22:19:37,435 - INFO - - 华达新材(605158.SH): 3.59%
2025-08-04 22:19:37,435 - INFO - - 广西能源(600310.SH): 0.25%
2025-08-04 22:19:37,436 - INFO - 特征列数量: 163, 期望特征数: 227
