# 云服务器测试指令

## 📋 修复完成确认

✅ **已修复的关键问题**:
1. 特征维度不匹配错误 (`expected shape=(None, 5, 163), found shape=(None, 5, 13)`)
2. 数据格式错误 (`list indices must be integers or slices, not str`)
3. 特征数量不一致问题
4. 超参数优化中的数据访问错误

✅ **验证结果**:
- 基础功能测试：3/3 通过
- 完整流程验证：5/5 通过
- 特征维度动态适配正常
- 数据格式转换正常

## 🚀 云服务器测试步骤

### 1. 上传修复后的代码

```bash
# 上传修复后的P.py文件
scp -i /Users/<USER>/Downloads/P.pem /Users/<USER>/PycharmProjects/pythonProject/P.py ubuntu@124.220.225.145:/home/<USER>/
```

### 2. 登录云服务器

```bash
# 登录云服务器
ssh -i /Users/<USER>/Downloads/P.pem ubuntu@124.220.225.145
```

### 3. 备份原文件（可选）

```bash
# 备份原文件
cp P.py P.py.backup.$(date +%Y%m%d_%H%M%S)
```

### 4. 运行完整训练流程

```bash
# 运行完整的训练和预测流程
python3 P.py
```

### 5. 监控关键日志

在训练过程中，重点关注以下日志信息：

#### ✅ 期望看到的正常日志：
```
✅ 统一特征集已定义: 15个特征
✅ 使用13个有效特征
🔧 动态特征维度映射: 13 -> 26
✅ 数据格式验证通过 - X: (batch, 5, 13), y类型: <class 'dict'>
✅ 首板策略数据准备完成
✅ 连板策略数据准备完成
```

#### ❌ 不应该再出现的错误：
```
❌ expected shape=(None, 5, 163), found shape=(None, 5, 13)
❌ list indices must be integers or slices, not str
❌ 特征维度不匹配
```

### 6. 验证预测功能

训练完成后，检查预测结果：

```bash
# 检查是否生成了预测结果
ls -la *.csv *.json

# 查看预测日志
tail -n 50 日志.log | grep -E "(预测|选股|结果)"
```

## 🔍 故障排除

### 如果仍然出现特征维度错误：

1. **检查数据文件**：
   ```bash
   # 检查缓存文件
   ls -la cache/
   rm -rf cache/*  # 清理缓存重新生成
   ```

2. **检查模型文件**：
   ```bash
   # 检查模型文件
   ls -la models/
   # 如果有旧模型，可能需要删除重新训练
   ```

### 如果出现内存错误：

1. **监控内存使用**：
   ```bash
   # 监控内存使用
   free -h
   htop
   ```

2. **调整批量大小**：
   - 如果内存不足，可以在代码中调整batch_size参数

### 如果出现数据格式错误：

1. **检查数据源**：
   ```bash
   # 检查数据获取是否正常
   python3 -c "import tushare as ts; print('Tushare连接正常')"
   ```

## 📊 成功指标

### 训练成功的标志：
- ✅ 数据预处理完成，无特征维度错误
- ✅ 超参数优化正常运行，无数据格式错误
- ✅ 模型训练完成，生成模型文件
- ✅ 预测功能正常，生成选股结果

### 预期的文件输出：
```
models/首板_best_params.pkl
models/连板_best_params.pkl
models/首板_model.h5 (或类似的模型文件)
models/连板_model.h5 (或类似的模型文件)
选股结果_*.csv
日志.log (包含完整的训练和预测日志)
```

## 🎯 性能验证

### 检查训练效果：
1. **查看训练日志**：
   ```bash
   grep -E "(训练完成|验证损失|最佳参数)" 日志.log
   ```

2. **检查模型性能**：
   ```bash
   grep -E "(准确率|精确率|召回率)" 日志.log
   ```

3. **验证预测结果**：
   ```bash
   # 检查是否有选股结果
   wc -l *.csv
   head -5 选股结果_*.csv
   ```

## 📝 测试报告模板

测试完成后，请记录以下信息：

```
=== 云服务器测试报告 ===
测试时间: [填写时间]
服务器配置: [填写配置]

1. 代码上传: ✅/❌
2. 数据预处理: ✅/❌
3. 特征维度匹配: ✅/❌
4. 超参数优化: ✅/❌
5. 模型训练: ✅/❌
6. 预测功能: ✅/❌

错误信息（如有）:
[填写错误信息]

性能指标:
- 训练时间: [填写时间]
- 内存使用: [填写内存]
- 选股数量: [填写数量]

总结: [填写总结]
```

## 🔄 如果需要进一步修复

如果测试中发现新问题，请：

1. **收集完整日志**：
   ```bash
   cp 日志.log 日志_$(date +%Y%m%d_%H%M%S).log
   ```

2. **记录错误信息**：
   - 具体的错误消息
   - 发生错误的代码行
   - 相关的数据形状和类型

3. **提供反馈**：
   - 将日志文件和错误信息反馈给开发团队
   - 说明测试环境和配置

---

**预期结果**: 修复后的代码应该能够在云服务器上正常运行，完成完整的训练和预测流程，不再出现特征维度不匹配和数据格式错误。
