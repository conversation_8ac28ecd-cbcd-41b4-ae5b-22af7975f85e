#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复objective函数中的缩进问题
"""

def batch_fix_indentation():
    """批量修复缩进"""
    
    # 读取文件
    with open('P.pull.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到objective函数的关键位置
    objective_start = None
    try_start = None
    except_start = None
    
    for i, line in enumerate(lines):
        if 'def objective(trial):' in line:
            objective_start = i
        elif objective_start is not None and line.strip() == 'try:':
            try_start = i
        elif try_start is not None and 'except Exception as e:' in line.strip():
            except_start = i
            break
    
    if not all([objective_start, try_start, except_start]):
        print("无法找到objective函数结构")
        return False
    
    print(f"找到objective函数结构:")
    print(f"  函数开始: 第{objective_start+1}行")
    print(f"  try开始: 第{try_start+1}行") 
    print(f"  except开始: 第{except_start+1}行")
    
    # 修复try块内的缩进
    fixed_lines = []
    
    for i, line in enumerate(lines):
        if try_start < i < except_start:
            # 在try块内
            stripped = line.strip()
            if stripped == '':
                # 空行保持
                fixed_lines.append(line)
            elif stripped.startswith('#'):
                # 注释行，使用12个空格缩进
                fixed_lines.append('            ' + stripped + '\n')
            elif line.startswith('        ') and not line.startswith('            '):
                # 只有8个空格缩进的行，需要修复为12个空格
                fixed_lines.append('    ' + line)  # 添加4个空格
            elif line.startswith('    ') and not line.startswith('        '):
                # 只有4个空格缩进的行，需要修复为12个空格
                fixed_lines.append('        ' + line)  # 添加8个空格
            elif not line.startswith(' '):
                # 没有缩进的行，添加12个空格
                fixed_lines.append('            ' + stripped + '\n')
            else:
                # 其他情况保持原样
                fixed_lines.append(line)
        else:
            # 不在try块内，保持原样
            fixed_lines.append(line)
    
    # 写回文件
    with open('P.pull.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print("✅ 批量缩进修复完成")
    return True

def main():
    """主函数"""
    print("🔧 开始批量修复缩进...")
    
    if batch_fix_indentation():
        print("🎉 缩进修复完成！")
        return True
    else:
        print("❌ 缩进修复失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
