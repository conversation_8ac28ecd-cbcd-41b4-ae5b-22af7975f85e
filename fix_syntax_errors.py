#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复P.pull.py中的语法错误
"""

import ast
import sys

def check_syntax_errors(file_path):
    """检查Python文件的语法错误"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        print("✅ 语法检查通过，没有发现语法错误")
        return True
        
    except SyntaxError as e:
        print(f"❌ 发现语法错误:")
        print(f"   文件: {e.filename}")
        print(f"   行号: {e.lineno}")
        print(f"   列号: {e.offset}")
        print(f"   错误: {e.msg}")
        print(f"   代码: {e.text.strip() if e.text else 'N/A'}")
        return False
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 检查P.pull.py语法错误...")
    
    file_path = "P.pull.py"
    
    if check_syntax_errors(file_path):
        print("🎉 语法检查完成，文件没有语法错误！")
        return True
    else:
        print("⚠️ 发现语法错误，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
