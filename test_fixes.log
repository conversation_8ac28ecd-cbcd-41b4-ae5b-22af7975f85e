2025-08-04 19:59:22,054 - INFO - 🚀 开始测试P.pull.py关键修复
2025-08-04 19:59:22,054 - INFO - ============================================================
2025-08-04 19:59:22,054 - INFO - 🧪 测试1: 不同板块的回归范围限制
2025-08-04 19:59:22,055 - INFO -   000001.SZ -> MAIN -> 最大涨跌幅: ±10.0%, 最小IQR: 8.0
2025-08-04 19:59:22,055 - INFO -   300001.SZ -> CHINEXT -> 最大涨跌幅: ±20.0%, 最小IQR: 12.0
2025-08-04 19:59:22,055 - INFO -   600001.SH -> MAIN -> 最大涨跌幅: ±10.0%, 最小IQR: 8.0
2025-08-04 19:59:22,055 - INFO -   688001.SH -> STAR -> 最大涨跌幅: ±20.0%, 最小IQR: 12.0
2025-08-04 19:59:22,055 - INFO -   430001.BJ -> BSE -> 最大涨跌幅: ±30.0%, 最小IQR: 18.0
2025-08-04 19:59:22,055 - INFO - ✅ 测试1通过: 回归范围限制正确
2025-08-04 19:59:22,056 - INFO - 🧪 测试2: 特征维度映射
2025-08-04 19:59:22,056 - INFO -   首板策略: 13维 -> 32维
2025-08-04 19:59:22,056 - INFO -   连板策略: 13维 -> 38维
2025-08-04 19:59:22,056 - INFO -   首板策略: 50维 -> 64维
2025-08-04 19:59:22,056 - INFO -   连板策略: 50维 -> 76维
2025-08-04 19:59:22,057 - INFO -   首板策略: 227维 -> 128维
2025-08-04 19:59:22,057 - INFO -   连板策略: 227维 -> 153维
2025-08-04 19:59:22,057 - INFO - ✅ 测试2通过: 特征维度映射正确
2025-08-04 19:59:22,057 - INFO - 🧪 测试3: 样本生成逻辑
2025-08-04 19:59:22,929 - INFO -   总数据: 10800条
2025-08-04 19:59:22,930 - INFO -   首板样本: 2744条
2025-08-04 19:59:22,930 - INFO -   连板样本: 10800条
2025-08-04 19:59:22,930 - INFO - ✅ 首板样本数量合理
2025-08-04 19:59:22,930 - INFO - ✅ 连板样本数量合理
2025-08-04 19:59:22,931 - INFO - ✅ 测试3通过: 样本生成逻辑正确
2025-08-04 19:59:22,937 - INFO - 🧪 测试4: 优化器回退机制
2025-08-04 19:59:22,937 - WARNING - AdEMAMix优化器初始化失败，回退到Adam优化器: AdEMAMix初始化失败
2025-08-04 19:59:22,937 - INFO - ✅ 优化器回退机制正常工作
2025-08-04 19:59:22,938 - INFO - ✅ 测试4通过: 优化器回退机制正确
2025-08-04 19:59:22,938 - INFO - ============================================================
2025-08-04 19:59:22,938 - INFO - 🎉 所有测试通过！修复逻辑正确
2025-08-04 19:59:22,938 - INFO - ✅ 可以安全地应用这些修复到P.pull.py
2025-08-04 20:18:23,228 - INFO - 🚀 开始测试P.pull.py关键修复
2025-08-04 20:18:23,228 - INFO - ============================================================
2025-08-04 20:18:23,228 - INFO - 🧪 测试1: 不同板块的回归范围限制
2025-08-04 20:18:23,229 - INFO -   000001.SZ -> MAIN -> 最大涨跌幅: ±10.0%, 最小IQR: 8.0
2025-08-04 20:18:23,229 - INFO -   300001.SZ -> CHINEXT -> 最大涨跌幅: ±20.0%, 最小IQR: 12.0
2025-08-04 20:18:23,229 - INFO -   600001.SH -> MAIN -> 最大涨跌幅: ±10.0%, 最小IQR: 8.0
2025-08-04 20:18:23,229 - INFO -   688001.SH -> STAR -> 最大涨跌幅: ±20.0%, 最小IQR: 12.0
2025-08-04 20:18:23,229 - INFO -   430001.BJ -> BSE -> 最大涨跌幅: ±30.0%, 最小IQR: 18.0
2025-08-04 20:18:23,229 - INFO - ✅ 测试1通过: 回归范围限制正确
2025-08-04 20:18:23,229 - INFO - 🧪 测试2: 特征维度映射
2025-08-04 20:18:23,229 - INFO -   首板策略: 13维 -> 32维
2025-08-04 20:18:23,230 - INFO -   连板策略: 13维 -> 38维
2025-08-04 20:18:23,230 - INFO -   首板策略: 50维 -> 64维
2025-08-04 20:18:23,230 - INFO -   连板策略: 50维 -> 76维
2025-08-04 20:18:23,230 - INFO -   首板策略: 227维 -> 128维
2025-08-04 20:18:23,230 - INFO -   连板策略: 227维 -> 153维
2025-08-04 20:18:23,230 - INFO - ✅ 测试2通过: 特征维度映射正确
2025-08-04 20:18:23,231 - INFO - 🧪 测试3: 样本生成逻辑
2025-08-04 20:18:23,638 - INFO -   总数据: 10800条
2025-08-04 20:18:23,638 - INFO -   首板样本: 2744条
2025-08-04 20:18:23,638 - INFO -   连板样本: 10800条
2025-08-04 20:18:23,638 - INFO - ✅ 首板样本数量合理
2025-08-04 20:18:23,638 - INFO - ✅ 连板样本数量合理
2025-08-04 20:18:23,638 - INFO - ✅ 测试3通过: 样本生成逻辑正确
2025-08-04 20:18:23,640 - INFO - 🧪 测试4: 优化器回退机制
2025-08-04 20:18:23,641 - WARNING - AdEMAMix优化器初始化失败，回退到Adam优化器: AdEMAMix初始化失败
2025-08-04 20:18:23,642 - INFO - ✅ 优化器回退机制正常工作
2025-08-04 20:18:23,642 - INFO - ✅ 测试4通过: 优化器回退机制正确
2025-08-04 20:18:23,642 - INFO - 🧪 测试5: AdEMAMix优化器改进
2025-08-04 20:18:34,400 - INFO -   ✅ 形状匹配，可以正常更新EMA
2025-08-04 20:18:34,400 - INFO - ✅ AdEMAMix形状兼容性处理正确
2025-08-04 20:18:34,400 - INFO - ✅ 测试5通过: AdEMAMix优化器改进正确
2025-08-04 20:18:34,400 - INFO - 🧪 测试6: 样本筛选条件放宽
2025-08-04 20:18:34,406 - INFO -   严格首板条件: 28个样本
2025-08-04 20:18:34,406 - INFO -   放宽首板条件: 274个样本
2025-08-04 20:18:34,406 - INFO -   样本增加: 246个 (878.6%)
2025-08-04 20:18:34,406 - INFO - ✅ 样本筛选条件放宽有效，样本数量显著增加
2025-08-04 20:18:34,407 - INFO - ✅ 测试6通过: 样本筛选条件放宽正确
2025-08-04 20:18:34,407 - INFO - ============================================================
2025-08-04 20:18:34,409 - INFO - 🎉 所有测试通过！修复逻辑正确
2025-08-04 20:18:34,409 - INFO - ✅ 可以安全地应用这些修复到P.pull.py
2025-08-04 20:18:34,409 - INFO - 🔧 主要修复内容:
2025-08-04 20:18:34,410 - INFO -   1. ✅ 不同板块回归范围限制（主板10%，创业板/科创板20%，北交所30%）
2025-08-04 20:18:34,410 - INFO -   2. ✅ AdEMAMix优化器形状兼容性改进
2025-08-04 20:18:34,410 - INFO -   3. ✅ 动态智能特征维度映射
2025-08-04 20:18:34,410 - INFO -   4. ✅ 样本筛选条件大幅放宽，增加样本数量
2025-08-04 20:18:34,410 - INFO -   5. ✅ 序列长度降低到2天，最大化保留样本
2025-08-04 20:18:34,413 - INFO -   6. ✅ 数据质量检查标准放宽
2025-08-04 20:46:07,716 - INFO - 🚀 开始测试P.pull.py关键修复
2025-08-04 20:46:07,717 - INFO - ============================================================
2025-08-04 20:46:07,717 - INFO - 🧪 测试1: 不同板块的回归范围限制
2025-08-04 20:46:07,718 - INFO -   000001.SZ -> MAIN -> 最大涨跌幅: ±10.0%, 最小IQR: 8.0
2025-08-04 20:46:07,718 - INFO -   300001.SZ -> CHINEXT -> 最大涨跌幅: ±20.0%, 最小IQR: 12.0
2025-08-04 20:46:07,719 - INFO -   600001.SH -> MAIN -> 最大涨跌幅: ±10.0%, 最小IQR: 8.0
2025-08-04 20:46:07,720 - INFO -   688001.SH -> STAR -> 最大涨跌幅: ±20.0%, 最小IQR: 12.0
2025-08-04 20:46:07,720 - INFO -   430001.BJ -> BSE -> 最大涨跌幅: ±30.0%, 最小IQR: 18.0
2025-08-04 20:46:07,720 - INFO - ✅ 测试1通过: 回归范围限制正确
2025-08-04 20:46:07,721 - INFO - 🧪 测试2: 特征维度映射
2025-08-04 20:46:07,721 - INFO -   首板策略: 13维 -> 32维
2025-08-04 20:46:07,721 - INFO -   连板策略: 13维 -> 38维
2025-08-04 20:46:07,721 - INFO -   首板策略: 50维 -> 64维
2025-08-04 20:46:07,721 - INFO -   连板策略: 50维 -> 76维
2025-08-04 20:46:07,721 - INFO -   首板策略: 227维 -> 128维
2025-08-04 20:46:07,721 - INFO -   连板策略: 227维 -> 153维
2025-08-04 20:46:07,721 - INFO - ✅ 测试2通过: 特征维度映射正确
2025-08-04 20:46:07,722 - INFO - 🧪 测试3: 样本生成逻辑
2025-08-04 20:46:08,659 - INFO -   总数据: 10800条
2025-08-04 20:46:08,660 - INFO -   首板样本: 2744条
2025-08-04 20:46:08,660 - INFO -   连板样本: 10800条
2025-08-04 20:46:08,660 - INFO - ✅ 首板样本数量合理
2025-08-04 20:46:08,660 - INFO - ✅ 连板样本数量合理
2025-08-04 20:46:08,660 - INFO - ✅ 测试3通过: 样本生成逻辑正确
2025-08-04 20:46:08,666 - INFO - 🧪 测试4: 优化器回退机制
2025-08-04 20:46:08,666 - WARNING - AdEMAMix优化器初始化失败，回退到Adam优化器: AdEMAMix初始化失败
2025-08-04 20:46:08,666 - INFO - ✅ 优化器回退机制正常工作
2025-08-04 20:46:08,666 - INFO - ✅ 测试4通过: 优化器回退机制正确
2025-08-04 20:46:08,666 - INFO - 🧪 测试5: AdEMAMix优化器改进
2025-08-04 20:46:26,031 - INFO -   ✅ 形状匹配，可以正常更新EMA
2025-08-04 20:46:26,037 - INFO - ✅ AdEMAMix形状兼容性处理正确
2025-08-04 20:46:26,037 - INFO - ✅ 测试5通过: AdEMAMix优化器改进正确
2025-08-04 20:46:26,040 - INFO - 🧪 测试6: 样本筛选条件放宽
2025-08-04 20:46:26,049 - INFO -   严格首板条件: 28个样本
2025-08-04 20:46:26,049 - INFO -   放宽首板条件: 274个样本
2025-08-04 20:46:26,050 - INFO -   样本增加: 246个 (878.6%)
2025-08-04 20:46:26,050 - INFO - ✅ 样本筛选条件放宽有效，样本数量显著增加
2025-08-04 20:46:26,050 - INFO - ✅ 测试6通过: 样本筛选条件放宽正确
2025-08-04 20:46:26,050 - INFO - 🧪 测试7: 增强样本生成逻辑
2025-08-04 20:46:26,057 - INFO -   首板日期: 20250105
2025-08-04 20:46:26,058 - INFO -   生成样本数量: 1
2025-08-04 20:46:26,058 - INFO -     样本类型: 首板次日连板, 标签: 1, 权重: 1.5
2025-08-04 20:46:26,058 - INFO - ✅ 样本类型识别正确
2025-08-04 20:46:26,059 - INFO - ✅ 正样本标签正确
2025-08-04 20:46:26,059 - INFO - ✅ 测试7通过: 增强样本生成逻辑正确
2025-08-04 20:46:26,059 - INFO - ============================================================
2025-08-04 20:46:26,059 - INFO - 🎉 所有测试通过！修复逻辑正确
2025-08-04 20:46:26,059 - INFO - ✅ 可以安全地应用这些修复到P.pull.py
2025-08-04 20:46:26,060 - INFO - 🔧 主要修复内容:
2025-08-04 20:46:26,060 - INFO -   1. ✅ 不同板块回归范围限制（主板10%，创业板/科创板20%，北交所30%）
2025-08-04 20:46:26,060 - INFO -   2. ✅ AdEMAMix优化器形状兼容性改进
2025-08-04 20:46:26,060 - INFO -   3. ✅ 动态智能特征维度映射
2025-08-04 20:46:26,060 - INFO -   4. ✅ 样本筛选条件大幅放宽，增加样本数量
2025-08-04 20:46:26,061 - INFO -   5. ✅ 序列长度恢复到10天，参考P.py成功实现
2025-08-04 20:46:26,061 - INFO -   6. ✅ 数据质量检查标准放宽
2025-08-04 20:46:26,061 - INFO -   7. ✅ 增强样本生成逻辑，大幅增加样本多样性
2025-08-04 21:01:03,101 - INFO - 🚀 开始测试P.pull.py关键修复
2025-08-04 21:01:03,102 - INFO - ============================================================
2025-08-04 21:01:03,102 - INFO - 🧪 测试1: 不同板块的回归范围限制
2025-08-04 21:01:03,102 - INFO -   000001.SZ -> MAIN -> 最大涨跌幅: ±10.0%, 最小IQR: 8.0
2025-08-04 21:01:03,102 - INFO -   300001.SZ -> CHINEXT -> 最大涨跌幅: ±20.0%, 最小IQR: 12.0
2025-08-04 21:01:03,102 - INFO -   600001.SH -> MAIN -> 最大涨跌幅: ±10.0%, 最小IQR: 8.0
2025-08-04 21:01:03,103 - INFO -   688001.SH -> STAR -> 最大涨跌幅: ±20.0%, 最小IQR: 12.0
2025-08-04 21:01:03,103 - INFO -   430001.BJ -> BSE -> 最大涨跌幅: ±30.0%, 最小IQR: 18.0
2025-08-04 21:01:03,103 - INFO - ✅ 测试1通过: 回归范围限制正确
2025-08-04 21:01:03,103 - INFO - 🧪 测试2: 特征维度映射
2025-08-04 21:01:03,103 - INFO -   首板策略: 13维 -> 32维
2025-08-04 21:01:03,103 - INFO -   连板策略: 13维 -> 38维
2025-08-04 21:01:03,103 - INFO -   首板策略: 50维 -> 64维
2025-08-04 21:01:03,104 - INFO -   连板策略: 50维 -> 76维
2025-08-04 21:01:03,104 - INFO -   首板策略: 227维 -> 128维
2025-08-04 21:01:03,104 - INFO -   连板策略: 227维 -> 153维
2025-08-04 21:01:03,104 - INFO - ✅ 测试2通过: 特征维度映射正确
2025-08-04 21:01:03,104 - INFO - 🧪 测试3: 样本生成逻辑
2025-08-04 21:01:03,502 - INFO -   总数据: 10800条
2025-08-04 21:01:03,502 - INFO -   首板样本: 2744条
2025-08-04 21:01:03,503 - INFO -   连板样本: 10800条
2025-08-04 21:01:03,503 - INFO - ✅ 首板样本数量合理
2025-08-04 21:01:03,503 - INFO - ✅ 连板样本数量合理
2025-08-04 21:01:03,503 - INFO - ✅ 测试3通过: 样本生成逻辑正确
2025-08-04 21:01:03,505 - INFO - 🧪 测试4: 优化器回退机制
2025-08-04 21:01:03,505 - WARNING - AdEMAMix优化器初始化失败，回退到Adam优化器: AdEMAMix初始化失败
2025-08-04 21:01:03,505 - INFO - ✅ 优化器回退机制正常工作
2025-08-04 21:01:03,506 - INFO - ✅ 测试4通过: 优化器回退机制正确
2025-08-04 21:01:03,506 - INFO - 🧪 测试5: AdEMAMix优化器改进
2025-08-04 21:01:15,383 - INFO -   ✅ 形状匹配，可以正常更新EMA
2025-08-04 21:01:15,385 - INFO - ✅ AdEMAMix形状兼容性处理正确
2025-08-04 21:01:15,385 - INFO - ✅ 测试5通过: AdEMAMix优化器改进正确
2025-08-04 21:01:15,386 - INFO - 🧪 测试6: 样本筛选条件放宽
2025-08-04 21:01:15,394 - INFO -   严格首板条件: 28个样本
2025-08-04 21:01:15,395 - INFO -   放宽首板条件: 274个样本
2025-08-04 21:01:15,397 - INFO -   样本增加: 246个 (878.6%)
2025-08-04 21:01:15,397 - INFO - ✅ 样本筛选条件放宽有效，样本数量显著增加
2025-08-04 21:01:15,397 - INFO - ✅ 测试6通过: 样本筛选条件放宽正确
2025-08-04 21:01:15,398 - INFO - 🧪 测试7: 增强样本生成逻辑
2025-08-04 21:01:15,401 - INFO -   首板日期: 20250105
2025-08-04 21:01:15,401 - INFO -   生成样本数量: 1
2025-08-04 21:01:15,407 - INFO -     样本类型: 首板次日连板, 标签: 1, 权重: 1.5
2025-08-04 21:01:15,408 - INFO - ✅ 样本类型识别正确
2025-08-04 21:01:15,408 - INFO - ✅ 正样本标签正确
2025-08-04 21:01:15,408 - INFO - ✅ 测试7通过: 增强样本生成逻辑正确
2025-08-04 21:01:15,409 - INFO - 🧪 测试8: 连板策略增强样本生成逻辑
2025-08-04 21:01:15,414 - INFO -   连板日期: 20250107
2025-08-04 21:01:15,414 - INFO -   连续涨停天数: 3
2025-08-04 21:01:15,415 - INFO -   生成样本数量: 1
2025-08-04 21:01:15,415 - INFO -     样本类型: 连板继续涨停, 标签: 1, 权重: 2.0
2025-08-04 21:01:15,415 - INFO - ✅ 连板样本类型识别正确
2025-08-04 21:01:15,415 - INFO - ✅ 连板正样本标签正确
2025-08-04 21:01:15,415 - INFO - ✅ 连板样本权重正确
2025-08-04 21:01:15,415 - INFO - ✅ 测试8通过: 连板策略增强样本生成逻辑正确
2025-08-04 21:01:15,416 - INFO - ============================================================
2025-08-04 21:01:15,416 - INFO - 🎉 所有测试通过！修复逻辑正确
2025-08-04 21:01:15,416 - INFO - ✅ 可以安全地应用这些修复到P.pull.py
2025-08-04 21:01:15,416 - INFO - 🔧 主要修复内容:
2025-08-04 21:01:15,416 - INFO -   1. ✅ 不同板块回归范围限制（主板10%，创业板/科创板20%，北交所30%）
2025-08-04 21:01:15,416 - INFO -   2. ✅ AdEMAMix优化器形状兼容性改进
2025-08-04 21:01:15,416 - INFO -   3. ✅ 动态智能特征维度映射
2025-08-04 21:01:15,416 - INFO -   4. ✅ 样本筛选条件大幅放宽，增加样本数量
2025-08-04 21:01:15,416 - INFO -   5. ✅ 序列长度恢复到10天，参考P.py成功实现
2025-08-04 21:01:15,416 - INFO -   6. ✅ 数据质量检查标准放宽
2025-08-04 21:01:15,416 - INFO -   7. ✅ 首板策略增强样本生成逻辑，大幅增加样本多样性
2025-08-04 21:01:15,417 - INFO -   8. ✅ 连板策略增强样本生成逻辑，解决样本不平衡问题
