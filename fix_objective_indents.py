#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动修复objective函数的缩进问题
"""

def fix_objective_indentation():
    """修复objective函数的缩进"""
    
    # 读取文件
    with open('P.pull.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到objective函数的范围
    objective_start = None
    try_start = None
    except_start = None
    finally_start = None
    
    for i, line in enumerate(lines):
        if 'def objective(trial):' in line:
            objective_start = i
            print(f"找到objective函数开始: 第{i+1}行")
        elif objective_start is not None and line.strip() == 'try:' and try_start is None:
            try_start = i
            print(f"找到try语句开始: 第{i+1}行")
        elif try_start is not None and line.strip().startswith('except Exception as e:') and except_start is None:
            except_start = i
            print(f"找到except语句开始: 第{i+1}行")
        elif except_start is not None and line.strip() == 'finally:' and finally_start is None:
            finally_start = i
            print(f"找到finally语句开始: 第{i+1}行")
            break
    
    if not all([objective_start, try_start, except_start, finally_start]):
        print("无法找到objective函数的完整结构")
        return False
    
    print(f"objective函数结构:")
    print(f"  函数开始: 第{objective_start+1}行")
    print(f"  try开始: 第{try_start+1}行")
    print(f"  except开始: 第{except_start+1}行")
    print(f"  finally开始: 第{finally_start+1}行")
    
    # 修复缩进
    fixed_lines = []
    
    for i, line in enumerate(lines):
        if i < objective_start or i > finally_start + 10:  # 超出范围，保持原样
            fixed_lines.append(line)
        elif i == objective_start:
            # 函数定义行
            fixed_lines.append(line)
        elif i == objective_start + 1:
            # 文档字符串
            fixed_lines.append('        """优化目标函数（量化级增强版）"""\n')
        elif i == objective_start + 2:
            # nonlocal语句
            fixed_lines.append('        nonlocal X_train, X_test, y_train, y_test\n')
        elif i == objective_start + 3:
            # 空行
            fixed_lines.append('\n')
        elif i == objective_start + 4:
            # 注释
            fixed_lines.append('        # ✅ 修复：添加错误处理和文件清理\n')
        elif i == objective_start + 5:
            # import语句
            fixed_lines.append('        import uuid\n')
        elif i == objective_start + 6:
            # import语句
            fixed_lines.append('        import tempfile\n')
        elif i == objective_start + 7:
            # 变量赋值
            fixed_lines.append('        trial_id = str(uuid.uuid4())[:8]\n')
        elif i == objective_start + 8:
            # 变量赋值
            fixed_lines.append('        temp_files = []\n')
        elif i == objective_start + 9:
            # 空行
            fixed_lines.append('\n')
        elif i == try_start:
            # try语句
            fixed_lines.append('        try:\n')
        elif try_start < i < except_start:
            # try块内的代码，使用12个空格缩进
            stripped = line.strip()
            if stripped == '':
                fixed_lines.append('\n')
            elif stripped.startswith('#'):
                fixed_lines.append('            ' + stripped + '\n')
            else:
                # 根据代码类型确定缩进
                if any(stripped.startswith(keyword) for keyword in ['if ', 'elif ', 'else:', 'for ', 'while ', 'def ', 'class ', 'try:', 'except', 'finally:', 'with ']):
                    # 控制结构，使用基础缩进
                    fixed_lines.append('            ' + stripped + '\n')
                elif line.startswith('                '):
                    # 已经有深层缩进的代码，保持相对缩进
                    fixed_lines.append('                ' + stripped + '\n')
                else:
                    # 普通语句
                    fixed_lines.append('            ' + stripped + '\n')
        elif i == except_start:
            # except语句
            fixed_lines.append('        except Exception as e:\n')
        elif except_start < i < finally_start:
            # except块内的代码
            stripped = line.strip()
            if stripped == '':
                fixed_lines.append('\n')
            else:
                fixed_lines.append('            ' + stripped + '\n')
        elif i == finally_start:
            # finally语句
            fixed_lines.append('        finally:\n')
        elif i > finally_start:
            # finally块内的代码
            stripped = line.strip()
            if stripped == '':
                fixed_lines.append('\n')
            elif stripped.startswith('#'):
                fixed_lines.append('            ' + stripped + '\n')
            else:
                # 根据原始缩进确定新缩进
                if line.startswith('                '):
                    fixed_lines.append('                ' + stripped + '\n')
                else:
                    fixed_lines.append('            ' + stripped + '\n')
    
    # 写回文件
    with open('P.pull.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print("✅ objective函数缩进修复完成")
    return True

def main():
    """主函数"""
    print("🔧 开始修复objective函数缩进...")
    
    if fix_objective_indentation():
        print("🎉 缩进修复完成！")
        return True
    else:
        print("❌ 缩进修复失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
