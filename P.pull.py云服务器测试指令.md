# P.pull.py 云服务器测试指令

## 📋 修复完成确认

✅ **已修复的关键问题**:
1. 数据格式错误 (`TypeError: list indices must be integers or slices, not str`)
2. 特征维度不匹配错误 (`expected shape=(None, 5, 163), found shape=(None, 5, 13)`)
3. 模型构建错误 (`InvalidArgumentError: Incompatible shapes`)
4. 超参数优化中的数据访问错误

✅ **验证结果**:
- 数据格式转换测试：✅ 通过
- 特征维度动态映射测试：✅ 通过
- 特征选择逻辑测试：✅ 通过
- 模型输入形状处理测试：✅ 通过

## 🚀 云服务器测试步骤

### 1. 上传修复后的代码

```bash
# 上传修复后的P.pull.py文件
scp -i /Users/<USER>/Downloads/P.pem /Users/<USER>/PycharmProjects/pythonProject/P.pull.py ubuntu@124.220.225.145:/home/<USER>/
```

### 2. 登录云服务器

```bash
# 登录云服务器
ssh -i /Users/<USER>/Downloads/P.pem ubuntu@124.220.225.145
```

### 3. 备份原文件（可选）

```bash
# 备份原文件
cp P.pull.py P.pull.py.backup.$(date +%Y%m%d_%H%M%S)
```

### 4. 运行完整训练流程

```bash
# 运行完整的训练和预测流程
python3 P.pull.py
```

### 5. 监控关键日志

在训练过程中，重点关注以下日志信息：

#### ✅ 期望看到的正常日志：

**数据准备阶段**:
```
✅ 使用13个有效特征
🔧 使用实际特征数量: 13，避免使用预定义的FEATURE_COLUMNS(163个)
✅ 特征数量合理: 13
```

**超参数优化阶段**:
```
objective函数: y_train是列表格式，转换为字典格式
objective函数: y_test是列表格式，转换为字典格式
✅ 数据格式转换成功
```

**模型训练阶段**:
```
🔧 连板策略智能映射：输入13维 -> 映射到32维
模型输入维度: (batch_size, 5, 13)
✅ 模型构建成功
```

**预测阶段**:
```
使用特征数量: 13, 期望特征数: 13
实际使用的特征: ['pct_chg', 'vol', 'amount', 'turnover_rate', 'pe']...
✅ 预测功能正常
```

#### ❌ 不应该再出现的错误：

```
❌ TypeError: list indices must be integers or slices, not str
❌ ValueError: Input 0 of layer "model" is incompatible with the layer: expected shape=(None, 5, 163), found shape=(None, 5, 13)
❌ InvalidArgumentError: Incompatible shapes: [96,32] vs. [384,16]
❌ 特征维度不匹配
```

### 6. 验证训练结果

训练完成后，检查以下内容：

```bash
# 检查是否生成了模型文件
ls -la models/

# 检查是否生成了超参数文件
ls -la models/*_best_params.pkl

# 查看训练日志的关键部分
grep -E "(✅|❌|🔧)" 日志.log | tail -20

# 检查是否有错误
grep -E "(ERROR|Failed|failed)" 日志.log | tail -10
```

### 7. 验证预测功能

```bash
# 检查是否生成了预测结果
ls -la *.csv *.json

# 查看预测日志
tail -n 50 日志.log | grep -E "(预测|选股|结果)"

# 检查选股结果
if [ -f "选股结果_*.csv" ]; then
    echo "预测结果文件存在"
    head -5 选股结果_*.csv
else
    echo "预测结果文件不存在，检查日志"
fi
```

## 🔍 故障排除

### 如果仍然出现特征维度错误：

1. **检查数据文件**：
   ```bash
   # 检查缓存文件
   ls -la cache/
   rm -rf cache/*  # 清理缓存重新生成
   ```

2. **检查特征数量**：
   ```bash
   # 查看实际特征数量
   grep "使用.*个有效特征" 日志.log
   grep "实际特征数量" 日志.log
   ```

### 如果出现数据格式错误：

1. **检查数据转换日志**：
   ```bash
   grep "列表格式.*转换.*字典格式" 日志.log
   grep "数据格式转换" 日志.log
   ```

2. **验证数据源**：
   ```bash
   # 检查数据获取是否正常
   python3 -c "import tushare as ts; print('Tushare连接正常')"
   ```

### 如果出现内存错误：

1. **监控内存使用**：
   ```bash
   # 监控内存使用
   free -h
   htop
   ```

2. **调整批量大小**：
   - 如果内存不足，可以在代码中调整batch_size参数

## 📊 成功指标

### 训练成功的标志：

- ✅ 数据预处理完成，使用13个有效特征
- ✅ 超参数优化正常运行，无数据格式错误
- ✅ 模型训练完成，特征维度匹配
- ✅ 预测功能正常，生成选股结果

### 预期的文件输出：

```
models/首板_best_params.pkl
models/连板_best_params.pkl
models/首板_*.h5 (或类似的模型文件)
models/连板_*.h5 (或类似的模型文件)
选股结果_*.csv
日志.log (包含完整的训练和预测日志)
```

### 性能指标：

```bash
# 检查训练效果
grep -E "(训练完成|验证损失|最佳参数)" 日志.log

# 检查模型性能
grep -E "(准确率|精确率|召回率)" 日志.log

# 验证预测结果
wc -l *.csv 2>/dev/null || echo "没有CSV文件"
```

## 📝 测试报告模板

测试完成后，请记录以下信息：

```
=== P.pull.py 云服务器测试报告 ===
测试时间: [填写时间]
服务器配置: [填写配置]

1. 代码上传: ✅/❌
2. 数据预处理: ✅/❌ (特征数量: ___)
3. 特征维度匹配: ✅/❌ (实际: ___, 期望: ___)
4. 超参数优化: ✅/❌ (数据格式: ___)
5. 模型训练: ✅/❌ (首板: ___, 连板: ___)
6. 预测功能: ✅/❌ (选股数量: ___)

关键日志摘录:
[粘贴关键的成功/错误日志]

性能指标:
- 训练时间: [填写时间]
- 内存使用: [填写内存]
- 特征数量: [填写数量]
- 选股数量: [填写数量]

总结: [填写总结]
```

## 🔄 如果需要进一步修复

如果测试中发现新问题，请：

1. **收集完整日志**：
   ```bash
   cp 日志.log 日志_$(date +%Y%m%d_%H%M%S).log
   ```

2. **记录错误信息**：
   - 具体的错误消息
   - 发生错误的代码行
   - 相关的数据形状和类型

3. **提供反馈**：
   - 将日志文件和错误信息反馈给开发团队
   - 说明测试环境和配置

---

**预期结果**: 修复后的P.pull.py应该能够在云服务器上正常运行，完成完整的训练和预测流程，不再出现特征维度不匹配和数据格式错误。
