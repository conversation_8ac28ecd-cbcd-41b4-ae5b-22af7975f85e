#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复objective函数的所有缩进问题
"""

def final_fix_indentation():
    """最终修复所有缩进问题"""
    
    # 读取文件
    with open('P.pull.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # 找到objective函数的关键位置
    objective_start = None
    try_start = None
    except_start = None
    finally_start = None
    
    for i, line in enumerate(lines):
        if 'def objective(trial):' in line:
            objective_start = i
        elif objective_start is not None and line.strip() == 'try:' and try_start is None:
            try_start = i
        elif try_start is not None and 'except Exception as e:' in line.strip() and except_start is None:
            except_start = i
        elif except_start is not None and line.strip() == 'finally:' and finally_start is None:
            finally_start = i
            break
    
    if not all([objective_start, try_start, except_start, finally_start]):
        print("无法找到objective函数的完整结构")
        return False
    
    print(f"找到objective函数结构:")
    print(f"  函数开始: 第{objective_start+1}行")
    print(f"  try开始: 第{try_start+1}行")
    print(f"  except开始: 第{except_start+1}行")
    print(f"  finally开始: 第{finally_start+1}行")
    
    # 修复缩进
    fixed_lines = []
    
    for i, line in enumerate(lines):
        if i < objective_start or i > finally_start + 20:
            # 超出范围，保持原样
            fixed_lines.append(line)
        elif i == objective_start:
            # 函数定义行
            fixed_lines.append(line)
        elif objective_start < i < try_start:
            # 函数开始到try之间的代码，使用8个空格缩进
            stripped = line.strip()
            if stripped == '':
                fixed_lines.append('')
            else:
                fixed_lines.append('        ' + stripped)
        elif i == try_start:
            # try语句
            fixed_lines.append('        try:')
        elif try_start < i < except_start:
            # try块内的代码，使用12个空格缩进
            stripped = line.strip()
            if stripped == '':
                fixed_lines.append('')
            elif stripped.startswith('#'):
                # 注释
                fixed_lines.append('            ' + stripped)
            else:
                # 根据代码结构确定缩进级别
                indent_level = 12  # 基础缩进
                
                # 检查是否是控制结构的内部代码
                if any(keyword in stripped for keyword in ['if ', 'elif ', 'else:', 'for ', 'while ', 'def ', 'try:', 'except', 'finally:', 'with ']):
                    # 控制结构，使用基础缩进
                    fixed_lines.append(' ' * indent_level + stripped)
                elif stripped.startswith(('return ', 'break', 'continue', 'pass', 'raise ')):
                    # 控制语句，检查上下文
                    # 查找最近的控制结构
                    for j in range(i-1, max(try_start, i-10), -1):
                        prev_line = lines[j].strip()
                        if prev_line.endswith(':') and any(kw in prev_line for kw in ['if ', 'elif ', 'else:', 'for ', 'while ', 'def ', 'try:', 'except', 'finally:', 'with ']):
                            indent_level = 16
                            break
                    fixed_lines.append(' ' * indent_level + stripped)
                elif '=' in stripped and not stripped.startswith('='):
                    # 赋值语句，检查上下文
                    # 查找最近的控制结构
                    for j in range(i-1, max(try_start, i-5), -1):
                        prev_line = lines[j].strip()
                        if prev_line.endswith(':'):
                            indent_level = 16
                            break
                    fixed_lines.append(' ' * indent_level + stripped)
                elif stripped.startswith(('{', '}', '[', ']', '(', ')')):
                    # 括号，可能是字典或列表的一部分
                    # 查找最近的赋值或控制结构
                    for j in range(i-1, max(try_start, i-5), -1):
                        prev_line = lines[j].strip()
                        if '=' in prev_line or prev_line.endswith(':'):
                            indent_level = 20
                            break
                    fixed_lines.append(' ' * indent_level + stripped)
                elif stripped.startswith("'") or stripped.startswith('"'):
                    # 字符串，可能是字典的值
                    indent_level = 20
                    fixed_lines.append(' ' * indent_level + stripped)
                else:
                    # 其他语句，使用基础缩进
                    fixed_lines.append(' ' * indent_level + stripped)
        elif i == except_start:
            # except语句
            fixed_lines.append('        except Exception as e:')
        elif except_start < i < finally_start:
            # except块内的代码
            stripped = line.strip()
            if stripped == '':
                fixed_lines.append('')
            else:
                fixed_lines.append('            ' + stripped)
        elif i == finally_start:
            # finally语句
            fixed_lines.append('        finally:')
        else:
            # finally块内的代码
            stripped = line.strip()
            if stripped == '':
                fixed_lines.append('')
            elif line.startswith('                '):
                # 已经有深层缩进的代码
                fixed_lines.append('                ' + stripped)
            else:
                fixed_lines.append('            ' + stripped)
    
    # 写回文件
    with open('P.pull.py', 'w', encoding='utf-8') as f:
        f.write('\n'.join(fixed_lines))
    
    print("✅ 最终缩进修复完成")
    return True

def main():
    """主函数"""
    print("🔧 开始最终缩进修复...")
    
    if final_fix_indentation():
        print("🎉 缩进修复完成！")
        return True
    else:
        print("❌ 缩进修复失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
