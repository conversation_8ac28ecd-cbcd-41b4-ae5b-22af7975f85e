#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一次性修复objective函数中所有缩进问题
"""

def fix_all_indentation():
    """修复所有缩进问题"""
    
    # 读取文件
    with open('P.pull.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到objective函数的关键位置
    objective_start = None
    try_start = None
    except_start = None
    finally_start = None
    
    for i, line in enumerate(lines):
        if 'def objective(trial):' in line:
            objective_start = i
        elif objective_start is not None and line.strip() == 'try:' and try_start is None:
            try_start = i
        elif try_start is not None and 'except Exception as e:' in line.strip() and except_start is None:
            except_start = i
        elif except_start is not None and line.strip() == 'finally:' and finally_start is None:
            finally_start = i
            break
    
    if not all([objective_start, try_start, except_start, finally_start]):
        print("无法找到objective函数的完整结构")
        return False
    
    print(f"找到objective函数结构:")
    print(f"  函数开始: 第{objective_start+1}行")
    print(f"  try开始: 第{try_start+1}行")
    print(f"  except开始: 第{except_start+1}行")
    print(f"  finally开始: 第{finally_start+1}行")
    
    # 修复缩进
    fixed_lines = []
    
    for i, line in enumerate(lines):
        if i < objective_start or i > finally_start + 20:
            # 超出范围，保持原样
            fixed_lines.append(line)
        elif i == objective_start:
            # 函数定义行
            fixed_lines.append(line)
        elif objective_start < i < try_start:
            # 函数开始到try之间的代码，使用8个空格缩进
            stripped = line.strip()
            if stripped == '':
                fixed_lines.append('\n')
            else:
                fixed_lines.append('        ' + stripped + '\n')
        elif i == try_start:
            # try语句
            fixed_lines.append('        try:\n')
        elif try_start < i < except_start:
            # try块内的代码，使用12个空格缩进
            stripped = line.strip()
            if stripped == '':
                fixed_lines.append('\n')
            elif stripped.startswith('#'):
                # 注释
                fixed_lines.append('            ' + stripped + '\n')
            else:
                # 普通代码
                # 检查是否是控制结构的内部代码（需要更深的缩进）
                if (line.startswith('                ') or  # 已经有16个空格
                    any(prev_line.strip().endswith(':') for prev_line in lines[max(0, i-3):i] 
                        if prev_line.strip() and not prev_line.strip().startswith('#'))):
                    # 控制结构内部，使用16个空格
                    fixed_lines.append('                ' + stripped + '\n')
                else:
                    # 普通语句，使用12个空格
                    fixed_lines.append('            ' + stripped + '\n')
        elif i == except_start:
            # except语句
            fixed_lines.append('        except Exception as e:\n')
        elif except_start < i < finally_start:
            # except块内的代码
            stripped = line.strip()
            if stripped == '':
                fixed_lines.append('\n')
            else:
                fixed_lines.append('            ' + stripped + '\n')
        elif i == finally_start:
            # finally语句
            fixed_lines.append('        finally:\n')
        else:
            # finally块内的代码
            stripped = line.strip()
            if stripped == '':
                fixed_lines.append('\n')
            elif line.startswith('                '):
                # 已经有深层缩进的代码
                fixed_lines.append('                ' + stripped + '\n')
            else:
                fixed_lines.append('            ' + stripped + '\n')
    
    # 写回文件
    with open('P.pull.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print("✅ 所有缩进修复完成")
    return True

def main():
    """主函数"""
    print("🔧 开始修复所有缩进...")
    
    if fix_all_indentation():
        print("🎉 缩进修复完成！")
        return True
    else:
        print("❌ 缩进修复失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
