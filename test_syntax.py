#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试P.pull.py的语法和基本功能
"""

def test_basic_syntax():
    """测试基本语法"""
    try:
        import ast
        with open('P.pull.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        print("✅ 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: 第{e.lineno}行, 第{e.offset}列")
        print(f"错误信息: {e.msg}")
        if e.text:
            print(f"错误代码: {e.text.strip()}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_import():
    """测试导入功能"""
    try:
        # 尝试导入模块（不执行main函数）
        import sys
        import os
        
        # 添加当前目录到Python路径
        current_dir = os.getcwd()
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 尝试编译模块
        import py_compile
        py_compile.compile('P.pull.py', doraise=True)
        print("✅ 模块编译成功")
        return True
        
    except Exception as e:
        print(f"❌ 模块编译失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始测试P.pull.py...")
    
    # 测试语法
    syntax_ok = test_basic_syntax()
    
    # 测试导入
    import_ok = test_import()
    
    if syntax_ok and import_ok:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 测试失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
